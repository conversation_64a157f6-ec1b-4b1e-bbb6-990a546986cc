../../Scripts/accelerate-config.exe,sha256=FVkyKdFzuIjzxc4XZsQ5VAUtIQUdtkvzy9gHrRePiGU,108408
../../Scripts/accelerate-estimate-memory.exe,sha256=KbCIdPl-3pTsNcjlKUICDCBYBA50kVnsqDJ0cs4Pekg,108410
../../Scripts/accelerate-launch.exe,sha256=asCOi1iGB7H9_rqdeCxwwf9Us5fTv5ndYmaiP4b1JhU,108408
../../Scripts/accelerate.exe,sha256=gR3xWjuZI67DtTqsDgbXJlkRMgnvbl2PBDkLKI3lrbc,108416
accelerate-0.25.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
accelerate-0.25.0.dist-info/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
accelerate-0.25.0.dist-info/METADATA,sha256=DL4wZrx0fz23ANPJMsf3AEfIW6JSjd6FE-ygUXM9yRg,18188
accelerate-0.25.0.dist-info/RECORD,,
accelerate-0.25.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate-0.25.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
accelerate-0.25.0.dist-info/entry_points.txt,sha256=Z_KV59tIt4oZtUDEQ0w8JThJ6_1dd8vR8heH24DeAXI,238
accelerate-0.25.0.dist-info/top_level.txt,sha256=esVfdxTidsjQ90zsN_rPpjLFJ4ijRlx4mnLrG09hlt4,11
accelerate/__init__.py,sha256=Gc6AqtUxWrokzEBnuTNVsw-zlXeGCh3twIvn0n2w000,784
accelerate/__pycache__/__init__.cpython-310.pyc,,
accelerate/__pycache__/accelerator.cpython-310.pyc,,
accelerate/__pycache__/big_modeling.cpython-310.pyc,,
accelerate/__pycache__/checkpointing.cpython-310.pyc,,
accelerate/__pycache__/data_loader.cpython-310.pyc,,
accelerate/__pycache__/hooks.cpython-310.pyc,,
accelerate/__pycache__/launchers.cpython-310.pyc,,
accelerate/__pycache__/local_sgd.cpython-310.pyc,,
accelerate/__pycache__/logging.cpython-310.pyc,,
accelerate/__pycache__/memory_utils.cpython-310.pyc,,
accelerate/__pycache__/optimizer.cpython-310.pyc,,
accelerate/__pycache__/scheduler.cpython-310.pyc,,
accelerate/__pycache__/state.cpython-310.pyc,,
accelerate/__pycache__/tracking.cpython-310.pyc,,
accelerate/accelerator.py,sha256=aXIfMsjYVFObOvlM4aLXXv_S2D-hLX46bpNVePjrvmw,137676
accelerate/big_modeling.py,sha256=HMb5dnJeSGlhWt3ztO1nyvcjrLRvHCvb3KTryHeBlNA,25788
accelerate/checkpointing.py,sha256=JyP4TmTWjUqLIiwSRtnOxYIEVg0Gs-tdId2op7xWyB8,11372
accelerate/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/commands/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/__pycache__/accelerate_cli.cpython-310.pyc,,
accelerate/commands/__pycache__/env.cpython-310.pyc,,
accelerate/commands/__pycache__/estimate.cpython-310.pyc,,
accelerate/commands/__pycache__/launch.cpython-310.pyc,,
accelerate/commands/__pycache__/test.cpython-310.pyc,,
accelerate/commands/__pycache__/tpu.cpython-310.pyc,,
accelerate/commands/accelerate_cli.py,sha256=HAf0-_GVXAlQtTXyIJ3maN8BKOUKdKVL6W7TFPFojr0,1721
accelerate/commands/config/__init__.py,sha256=iJK8dgj3pc5Vdr1E7UuGoFu-BlybyXLxYDoTg9gXngE,1645
accelerate/commands/config/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/config/__pycache__/cluster.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config_args.cpython-310.pyc,,
accelerate/commands/config/__pycache__/config_utils.cpython-310.pyc,,
accelerate/commands/config/__pycache__/default.cpython-310.pyc,,
accelerate/commands/config/__pycache__/sagemaker.cpython-310.pyc,,
accelerate/commands/config/__pycache__/update.cpython-310.pyc,,
accelerate/commands/config/cluster.py,sha256=QM7ofIxRArep0wU8Yo8rESuXKRkXyuMNLLbdwieVvQs,28020
accelerate/commands/config/config.py,sha256=FuRlQvOjgATEtyqOSsGD-KEtOCvACOHjs2C-krrtldk,3035
accelerate/commands/config/config_args.py,sha256=neMxebxNBe2K3cO9pkvVE2Z7PK7Nx1x8mVOfo-RAVTc,9633
accelerate/commands/config/config_utils.py,sha256=Wpsl7zFDuSq4IGm-72DdJvdkwMe2cy-PxoJruxB7IpA,2913
accelerate/commands/config/default.py,sha256=LnqA8EnI7kRpjqYlTPM2ZAscxsB2XeIQZgGOaTTy2N4,5044
accelerate/commands/config/sagemaker.py,sha256=GjHE2-h4tRr1P_PFtMF3miiAtJlzkbHbMb6kFXqn8eo,10341
accelerate/commands/config/update.py,sha256=NXW1J7GkUHpg71QlIXsmMB_0z8S8IZo2FWax5POwrhc,2395
accelerate/commands/env.py,sha256=7guiNUOE0SFe2CRexn2FNmI-4yuwGOXSJLd3QG8tVpA,3056
accelerate/commands/estimate.py,sha256=xVSJ_4v471PNA8an6MMfykwWF4V3NdltGCpES0eVSiw,10429
accelerate/commands/launch.py,sha256=3GxJMHo5L6knaqJiW-9bC3c6AkmX-8Y5aYmx25XAwks,39729
accelerate/commands/menu/__init__.py,sha256=5EhDZN5_e1TAuh9_KqJ4Ghs61offoeGZy1pktSBDpa0,39
accelerate/commands/menu/__pycache__/__init__.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/cursor.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/helpers.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/input.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/keymap.cpython-310.pyc,,
accelerate/commands/menu/__pycache__/selection_menu.cpython-310.pyc,,
accelerate/commands/menu/cursor.py,sha256=-lmpJVAzvNc0c3EOtSuLoKB59zqylVCbYyWLPnrOmvQ,2028
accelerate/commands/menu/helpers.py,sha256=KrSB5fJjH4MUEUAQJ6bYaN16AYcnl9UalDrPD3DYeeg,1483
accelerate/commands/menu/input.py,sha256=uW2ywuqWPOKjkS7XBjqNpuVWLTgVKici2_xLyltEbMs,2581
accelerate/commands/menu/keymap.py,sha256=c9YEMMmNlBGtMiWFk2rdhtTSCZ9w_uJ77cNCwAKguHk,4087
accelerate/commands/menu/selection_menu.py,sha256=UZKwSIZKKG60y2fuWbSoCx0RbrPS4MbY2DwvxWRBIBQ,4920
accelerate/commands/test.py,sha256=whf_g7X263A5OErEHRzKu_L5x6HWbIIVNS8N5ERtGao,2179
accelerate/commands/tpu.py,sha256=OnFQNu9zhlK5D7xXouZZXJevN5623Jgy_HsHTuy4HAE,5553
accelerate/data_loader.py,sha256=Ek8OlkdH_ZFgL7sXbwfI5R3snO88P7Ao2DXYx58UeQc,45516
accelerate/hooks.py,sha256=2t_TaCzl9fTblk7PMHzT98ITBu1Ki_UZ_gVJz6JYAXQ,25320
accelerate/launchers.py,sha256=IV21ZWGbwjybbzvffTedkYKa6QhlHpn39tK7y1czmU8,11025
accelerate/local_sgd.py,sha256=znJcwwpRb0imRslW5_uQ4OYJmM8zxekMv4XTnbzXlZk,3924
accelerate/logging.py,sha256=kvUvk33r_7T2BNzIwqRZBOhuC-50Ju4rm4HbsM6h2G8,4897
accelerate/memory_utils.py,sha256=3R5LoeHl6GgTZ-IMPrDZMdaEehWarGdPqODushb-6pg,862
accelerate/optimizer.py,sha256=A48WIWqZgaq_MpWTfoK4QwOHorjk4EUK4fUeWBPr0jg,7205
accelerate/scheduler.py,sha256=des_4M_Tt1W8gCYZZbLla0GHBEgJY3Wx2EGBQPTzeiY,4238
accelerate/state.py,sha256=w5HhbYmMvVXmYoQkosBhf_KsIhz2ADnSjtozUT05REE,45926
accelerate/test_utils/__init__.py,sha256=wHZ1QtTBfvewlSapPz40qelbdtACZZ5UwYEcL87xfpU,531
accelerate/test_utils/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/__pycache__/examples.cpython-310.pyc,,
accelerate/test_utils/__pycache__/testing.cpython-310.pyc,,
accelerate/test_utils/__pycache__/training.cpython-310.pyc,,
accelerate/test_utils/examples.py,sha256=PJAAy5MjIeyH5Sgj9sFqh0VGebfI7Tg4i_3OBABVVYg,7301
accelerate/test_utils/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/test_utils/scripts/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_cli.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_distributed_data_loop.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_notebook.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ops.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_script.cpython-310.pyc,,
accelerate/test_utils/scripts/__pycache__/test_sync.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate/test_utils/scripts/external_deps/__pycache__/__init__.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_checkpointing.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_metrics.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_peak_memory_usage.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_performance.cpython-310.pyc,,
accelerate/test_utils/scripts/external_deps/test_checkpointing.py,sha256=eJ8dpY6Bi9De7Vb9oDw435NELTjWegjWD7wuckvkaoQ,10686
accelerate/test_utils/scripts/external_deps/test_metrics.py,sha256=ahmtl--BAkXKF53SUGi0oZdhWzB-2Tt3GMjLa3RfVJc,10973
accelerate/test_utils/scripts/external_deps/test_peak_memory_usage.py,sha256=xC5WLrPk87fSdf8Pr_JvM4BhjT4ngnHX831UK3BnZEw,10725
accelerate/test_utils/scripts/external_deps/test_performance.py,sha256=VqYjGaIK509389-iukIBb1397dFLbrWyHwjAKF3Fcvw,9093
accelerate/test_utils/scripts/test_cli.py,sha256=EJClouXlerf7cpgqY1P1VY2ohUcRXk56GoVkM6-jmrU,227
accelerate/test_utils/scripts/test_distributed_data_loop.py,sha256=TEqnW4WIlSDsMB9D2bcvAC_AB9hHtJRiERt3ZGKzK80,8236
accelerate/test_utils/scripts/test_notebook.py,sha256=d6jsUdF9fqUF5NUElOKN0xMaIy8-XLC6v1p1LH1LsEg,923
accelerate/test_utils/scripts/test_ops.py,sha256=998SE9udf66cyD0JLjEWZuKvQVZ13rSk_4KdnJ0qliY,5263
accelerate/test_utils/scripts/test_script.py,sha256=9P7IWVjjcFwbncDpKGSETjF1Cf5BCjihZpizQudN3zo,26246
accelerate/test_utils/scripts/test_sync.py,sha256=PHtgg7jZpkeE2hMjLRV7YGLRdNiatbR_G76tVVZKimY,17147
accelerate/test_utils/testing.py,sha256=MDOefXxT-o9i2AGI7bzHeTcbBdjvkh5YvIyK6KseY34,16101
accelerate/test_utils/training.py,sha256=7RNVMmRb6WFCvGzyR2tWTaPL5tKO4YGzjXN0GFWvI8U,4019
accelerate/tracking.py,sha256=uEmLvQcxj7743RB1t2raI5uuuF0CzIBJEV9oFhmh12g,38091
accelerate/utils/__init__.py,sha256=49Fs1eGnCgDuXRyXaHfpRYj6A1lr9sWxOF4SVuSy05g,5007
accelerate/utils/__pycache__/__init__.cpython-310.pyc,,
accelerate/utils/__pycache__/bnb.cpython-310.pyc,,
accelerate/utils/__pycache__/constants.cpython-310.pyc,,
accelerate/utils/__pycache__/dataclasses.cpython-310.pyc,,
accelerate/utils/__pycache__/deepspeed.cpython-310.pyc,,
accelerate/utils/__pycache__/environment.cpython-310.pyc,,
accelerate/utils/__pycache__/fsdp_utils.cpython-310.pyc,,
accelerate/utils/__pycache__/imports.cpython-310.pyc,,
accelerate/utils/__pycache__/launch.cpython-310.pyc,,
accelerate/utils/__pycache__/megatron_lm.cpython-310.pyc,,
accelerate/utils/__pycache__/memory.cpython-310.pyc,,
accelerate/utils/__pycache__/modeling.cpython-310.pyc,,
accelerate/utils/__pycache__/offload.cpython-310.pyc,,
accelerate/utils/__pycache__/operations.cpython-310.pyc,,
accelerate/utils/__pycache__/other.cpython-310.pyc,,
accelerate/utils/__pycache__/random.cpython-310.pyc,,
accelerate/utils/__pycache__/rich.cpython-310.pyc,,
accelerate/utils/__pycache__/torch_xla.cpython-310.pyc,,
accelerate/utils/__pycache__/tqdm.cpython-310.pyc,,
accelerate/utils/__pycache__/transformer_engine.cpython-310.pyc,,
accelerate/utils/__pycache__/versions.cpython-310.pyc,,
accelerate/utils/bnb.py,sha256=3i59dy8EcBYJEnT2alJ5_M-zeIpFsrceQ4bImiJJKOk,20570
accelerate/utils/constants.py,sha256=GOJAi8o1SpiZXlv5o3Kl7LiSV7EjGiVwDO2NKWl696I,2553
accelerate/utils/dataclasses.py,sha256=WKerJVr1kJHNgCnujDuv1LYTkghlHSykGqQsdXNj9cc,65465
accelerate/utils/deepspeed.py,sha256=X_JG9XW-ZGVmXGED84eC2akEkUt-YsNiewCSeW7DYLk,10208
accelerate/utils/environment.py,sha256=MnxE4dCp9ZVcnhvJ5f-GxBYf4BCt-MMdpEMyZg_FJJE,2641
accelerate/utils/fsdp_utils.py,sha256=ghKlDWRwmohtBt2QFyGVvEBqDB-KhO9QfZnW-P7jo04,9825
accelerate/utils/imports.py,sha256=d59sLOUbek41yLTII1Yp_0p6XAwYDxUlvkratB3BMpQ,9379
accelerate/utils/launch.py,sha256=N6r3GzkRSbjaH5p8YvazPQsGRnnhBlOto3Hnkqbz9Mg,25032
accelerate/utils/megatron_lm.py,sha256=yOrhJ2u9NKBO3LR_FWlIxh44PWUx_cgdcCVDAjrBiE8,57263
accelerate/utils/memory.py,sha256=d2DBzqkcoYAPlpK0aMQ5f5c-R-M6Wx9KBx_2UM6qhNw,4880
accelerate/utils/modeling.py,sha256=Vs4ui-b5f6zLvPC12GTw8iLOAFCpN6GvZi-5NXRZ_fw,68803
accelerate/utils/offload.py,sha256=klA9K5ZJBLjG8JoKA_VsY0O6ihTKejykN1VEEWpgRLU,7398
accelerate/utils/operations.py,sha256=eVU-xL7VkgBQawYy3kokd5JU5x4ud8EGMwpNhXzw7jU,25007
accelerate/utils/other.py,sha256=n-ahpXk5wWcTbwD4_jLNij6VKhZKzQ08MF6n-qKmUkI,10403
accelerate/utils/random.py,sha256=IWVnFFjRuZZOO8HI9L7suHRSM33Pk2NXYywOpU0BKIg,4292
accelerate/utils/rich.py,sha256=8JZX_uGMQX-BufdXxJpdne7BWd1KyLHSgbiGxrDMYr8,847
accelerate/utils/torch_xla.py,sha256=Pq1tuqN0X_pWDVza6YgjfO45uoJdoRVRForLeLQzFus,1908
accelerate/utils/tqdm.py,sha256=0cegNnuA93tKT3o6HDip90rPl8BODLFLu4jP1E3aJ08,1344
accelerate/utils/transformer_engine.py,sha256=IDqv903ai-b0Zv3bTijXfpXmdYom041JNC3OiW5vLaY,3561
accelerate/utils/versions.py,sha256=UgmcbjBm--6CIx1ZamSAMjAK_B_2l48LbeaNygqej8M,2149
