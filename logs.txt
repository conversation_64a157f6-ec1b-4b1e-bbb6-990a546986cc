2025-07-28 06:56:24 [INFO] ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 06:56:24 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 06:56:24 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 06:56:24 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 06:56:24 [INFO] 🔍 智能日志监控已启动
2025-07-28 06:56:24 [ERROR] 无法访问日志文件: [Errno 13] Permission denied: 'D:\\AIGC\\AI_Video\\index-tts\\logs.txt'
2025-07-28 06:56:24 [INFO] 🔍 错误分析: 🔒 文件权限不足
2025-07-28 06:56:24 [INFO] 💡 建议解决方案:
2025-07-28 06:56:24 [INFO]    1. 以管理员身份运行程序
2025-07-28 06:56:24 [INFO]    2. 检查文件和目录权限
2025-07-28 06:56:24 [INFO]    3. 关闭占用文件的其他程序
2025-07-28 06:56:24 [INFO]    4. 更改文件所有者权限
2025-07-28 06:56:24 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 06:56:24 [INFO] 💡 解决方案:
2025-07-28 06:56:24 [INFO] 1. 关闭所有打开logs.txt的程序
2025-07-28 06:56:24 [INFO] 2. 以管理员身份运行程序
2025-07-28 06:56:24 [INFO] 3. 检查文件权限设置
2025-07-28 06:56:24 [WARNING] 程序将继续运行，日志仅输出到控制台
⚠️ 日志文件初始化失败: [Errno 13] Permission denied: 'D:\\AIGC\\AI_Video\\index-tts\\logs.txt'
💡 解决方案:
1. 检查文件权限
2. 确保目录存在
3. 以管理员身份运行
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 📋 智能错误分析报告
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 06:56:26,040 [INFO] ⚠️  严重程度: 中
2025-07-28 06:56:26,040 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 06:56:26,040 [INFO] 💡 解决方案:
2025-07-28 06:56:26,040 [INFO]    1. 以管理员身份运行程序
2025-07-28 06:56:26,040 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 06:56:26,040 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 06:56:26,040 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 06:56:26,040 [INFO] ============================================================
2025-07-28 06:56:26,040 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 06:56:26,054 [INFO] === IndexTTS 系统检查 ===
2025-07-28 06:56:26,054 [INFO] Python版本: 3.10.16
2025-07-28 06:56:26,054 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 06:56:26,073 [INFO] ✅ CUDA可用: 是
2025-07-28 06:56:26,073 [INFO] CUDA版本: 12.1
2025-07-28 06:56:26,073 [INFO] GPU数量: 1
2025-07-28 06:56:26,076 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 06:56:26,076 [INFO] 磁盘空间: 总计 631 GB, 已用 317 GB, 可用 313 GB
2025-07-28 06:56:26,076 [INFO] === 系统检查完成 ===
2025-07-28 06:56:26,078 [INFO] 🔄 开始: 初始化IndexTTS模型
Detected CUDA files, patching ldflags
Emitting ninja build file D:\AIGC\AI_Video\index-tts\indextts\BigVGAN\alias_free_activation\cuda\build\build.ninja...
INFO: Could not find files for the given pattern(s).
>> Failed to load custom CUDA kernel for BigVGAN. Falling back to torch. Command '['where', 'cl']' returned non-zero exit status 1.
 Reinstall with `pip install -e . --no-deps --no-build-isolation` to prebuild `anti_alias_activation_cuda` kernel.
See more details: https://github.com/index-tts/index-tts/issues/164#issuecomment-2903453206
>> 检测到CUDA设备: cuda:0
>> GPU型号: NVIDIA GeForce RTX 2060
>> 显存大小: 12.0 GB
>> 计算能力: 7.5
>> ✅ 检测到RTX 20系显卡，完全支持
>> ✅ 计算能力良好，推荐配置
>> GPT weights restored from: checkpoints\gpt.pth
>> DeepSpeed加载失败，回退到标准推理: No module named 'deepspeed'
See more details https://www.deepspeed.ai/tutorials/advanced-install/
2025-07-28 06:56:36,067 [INFO] ============================================================
2025-07-28 06:56:36,067 [INFO] 📋 智能错误分析报告
2025-07-28 06:56:36,067 [INFO] ============================================================
2025-07-28 06:56:36,067 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 06:56:36,068 [INFO] ⚠️  严重程度: 低
2025-07-28 06:56:36,068 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 06:56:36,068 [INFO] 💡 解决方案:
2025-07-28 06:56:36,068 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 06:56:36,068 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 06:56:36,068 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 06:56:36,068 [INFO] ============================================================
2025-07-28 06:56:36,068 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 06:56:36,827 WETEXT INFO found existing fst: D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_tagger.fst
Removing weight norm...
>> bigvgan weights restored from: checkpoints\bigvgan_generator.pth
2025-07-28 06:56:36,827 [INFO] found existing fst: D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 06:56:36,827 WETEXT INFO                     D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 06:56:36,827 [INFO]                     D:\AIGC\AI_Video\index-tts\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 06:56:36,827 WETEXT INFO skip building fst for zh_normalizer ...
2025-07-28 06:56:36,827 [INFO] skip building fst for zh_normalizer ...
2025-07-28 06:56:37,240 WETEXT INFO found existing fst: D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 06:56:37,240 [INFO] found existing fst: D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 06:56:37,240 WETEXT INFO                     D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 06:56:37,240 [INFO]                     D:\AIGC\AI_Video\index-tts\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 06:56:37,240 WETEXT INFO skip building fst for en_normalizer ...
2025-07-28 06:56:37,240 [INFO] skip building fst for en_normalizer ...
>> TextNormalizer loaded
>> bpe model loaded from: checkpoints\bpe.model
2025-07-28 06:56:37,979 [INFO] ✅ IndexTTS模型初始化成功
2025-07-28 06:56:38,255 [INFO] 🔍 找到可用端口: 7866
2025-07-28 06:56:38,255 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 06:56:38,255 [INFO] 🌐 访问地址: http://127.0.0.1:7866
* Running on local URL:  http://127.0.0.1:7866
2025-07-28 06:56:38,352 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 06:56:38,372 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
* To create a public link, set `share=True` in `launch()`.
2025-07-28 06:56:38,953 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:16:08 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:16:08 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:16:08 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:16:08 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:16:08 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:16:08 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:16:08 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:16:08 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:16:08 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:16:08 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:16:08 [INFO] ============================================================
2025-07-28 22:16:08 [INFO] 📋 智能错误分析报告
2025-07-28 22:16:08,185 [INFO] ============================================================
2025-07-28 22:16:08,185 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:16:08,185 [INFO] ⚠️  严重程度: 中
2025-07-28 22:16:08,185 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:16:08,186 [INFO] 💡 解决方案:
2025-07-28 22:16:08,186 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:16:08,186 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:16:08,187 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:16:08,187 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:16:08,187 [INFO] ============================================================
2025-07-28 22:16:08,187 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:16:08,187 [INFO] ============================================================
2025-07-28 22:16:08,188 [INFO] 📋 智能错误分析报告
2025-07-28 22:16:08,188 [INFO] ============================================================
2025-07-28 22:16:08,188 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:16:08,188 [INFO] ⚠️  严重程度: 低
2025-07-28 22:16:08,188 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:16:08,188 [INFO] 💡 解决方案:
2025-07-28 22:16:08,188 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:16:08,188 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:16:08,189 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:16:08,189 [INFO] ============================================================
2025-07-28 22:16:08,189 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:16:10,503 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:16:10,504 [INFO] Python版本: 3.10.16
2025-07-28 22:16:10,504 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:16:10,504 [INFO] ✅ CUDA可用: 是
2025-07-28 22:16:10,504 [INFO] CUDA版本: 12.1
2025-07-28 22:16:10,504 [INFO] GPU数量: 1
2025-07-28 22:16:10,504 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:16:10,504 [INFO] 磁盘空间: 总计 631 GB, 已用 290 GB, 可用 341 GB
2025-07-28 22:16:10,504 [INFO] === 系统检查完成 ===
2025-07-28 22:16:10,506 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:16:23,060 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:16:23,060 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:16:23,060 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:16:23,560 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:16:23,561 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:16:23,561 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:16:24,366 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:16:24,685 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:16:24,685 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:16:24,686 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:16:24,800 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:16:24,821 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:16:25,409 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:42:55 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:42:55 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:42:55 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:42:55 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:42:55 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:42:55 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:42:55 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:42:55 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:42:55 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:42:55 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:42:55 [INFO] ============================================================
2025-07-28 22:42:55 [INFO] 📋 智能错误分析报告
2025-07-28 22:42:55,928 [INFO] ============================================================
2025-07-28 22:42:55,928 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:42:55,928 [INFO] ⚠️  严重程度: 中
2025-07-28 22:42:55,928 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:42:55,928 [INFO] 💡 解决方案:
2025-07-28 22:42:55,928 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:42:55,928 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:42:55,928 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:42:55,929 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:42:55,929 [INFO] ============================================================
2025-07-28 22:42:55,929 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:42:55,930 [INFO] ============================================================
2025-07-28 22:42:55,930 [INFO] 📋 智能错误分析报告
2025-07-28 22:42:55,930 [INFO] ============================================================
2025-07-28 22:42:55,931 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:42:55,931 [INFO] ⚠️  严重程度: 低
2025-07-28 22:42:55,932 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:42:55,932 [INFO] 💡 解决方案:
2025-07-28 22:42:55,932 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:42:55,932 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:42:55,932 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:42:55,933 [INFO] ============================================================
2025-07-28 22:42:55,933 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:43:00,198 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:43:00,198 [INFO] Python版本: 3.10.16
2025-07-28 22:43:00,198 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:43:00,198 [INFO] ✅ CUDA可用: 是
2025-07-28 22:43:00,198 [INFO] CUDA版本: 12.1
2025-07-28 22:43:00,198 [INFO] GPU数量: 1
2025-07-28 22:43:00,198 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:43:00,199 [INFO] 磁盘空间: 总计 631 GB, 已用 289 GB, 可用 341 GB
2025-07-28 22:43:00,199 [INFO] === 系统检查完成 ===
2025-07-28 22:43:00,203 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:43:17,277 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:43:17,277 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:43:17,278 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:43:17,728 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:43:17,728 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:43:17,728 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:43:18,611 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:43:19,133 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:43:19,133 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:43:19,133 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:43:19,310 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:43:19,343 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:43:20,118 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:45:24 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:45:24 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:45:24 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:45:24 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:45:24 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:45:24 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:45:24 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:45:24 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:45:24 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:45:24 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:45:24,366 [INFO] 📋 智能错误分析报告
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:45:24,366 [INFO] ⚠️  严重程度: 中
2025-07-28 22:45:24,366 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:45:24,366 [INFO] 💡 解决方案:
2025-07-28 22:45:24,366 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:45:24,366 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:45:24,366 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:45:24,366 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 📋 智能错误分析报告
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:45:24,366 [INFO] ⚠️  严重程度: 低
2025-07-28 22:45:24,366 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:45:24,366 [INFO] 💡 解决方案:
2025-07-28 22:45:24,366 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:45:24,366 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:45:24,366 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:45:24,366 [INFO] ============================================================
2025-07-28 22:45:24,366 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:45:26,799 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:45:26,799 [INFO] Python版本: 3.10.16
2025-07-28 22:45:26,799 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:45:26,799 [INFO] ✅ CUDA可用: 是
2025-07-28 22:45:26,799 [INFO] CUDA版本: 12.1
2025-07-28 22:45:26,799 [INFO] GPU数量: 1
2025-07-28 22:45:26,799 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:45:26,799 [INFO] 磁盘空间: 总计 631 GB, 已用 289 GB, 可用 341 GB
2025-07-28 22:45:26,799 [INFO] === 系统检查完成 ===
2025-07-28 22:45:26,799 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:45:38,007 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:45:38,007 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:45:38,007 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:45:38,356 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:45:38,356 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:45:38,356 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:45:39,008 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:45:39,261 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:45:39,261 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:45:39,261 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:45:39,372 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:45:39,403 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:45:39,986 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:52:01 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:52:01 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:52:01 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:52:01 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:52:01 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:52:01 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:52:01 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:52:01 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:52:01 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:52:01 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:52:01 [INFO] ============================================================
2025-07-28 22:52:01 [INFO] 📋 智能错误分析报告
2025-07-28 22:52:01,157 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:52:01,157 [INFO] ⚠️  严重程度: 中
2025-07-28 22:52:01,158 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:52:01,159 [INFO] 💡 解决方案:
2025-07-28 22:52:01,159 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:52:01,160 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:52:01,160 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:52:01,160 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:52:01,160 [INFO] ============================================================
2025-07-28 22:52:01,160 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:52:01,160 [INFO] ============================================================
2025-07-28 22:52:01,160 [INFO] 📋 智能错误分析报告
2025-07-28 22:52:01,160 [INFO] ============================================================
2025-07-28 22:52:01,160 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:52:01,160 [INFO] ⚠️  严重程度: 低
2025-07-28 22:52:01,160 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:52:01,160 [INFO] 💡 解决方案:
2025-07-28 22:52:01,160 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:52:01,160 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:52:01,160 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:52:01,160 [INFO] ============================================================
2025-07-28 22:52:01,160 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:52:02,967 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:52:02,970 [INFO] Python版本: 3.10.16
2025-07-28 22:52:02,970 [INFO] PyTorch版本: 2.5.1+cu121
2025-07-28 22:52:02,970 [INFO] ✅ CUDA可用: 是
2025-07-28 22:52:02,970 [INFO] CUDA版本: 12.1
2025-07-28 22:52:02,970 [INFO] GPU数量: 1
2025-07-28 22:52:02,970 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:52:02,970 [INFO] 磁盘空间: 总计 631 GB, 已用 289 GB, 可用 341 GB
2025-07-28 22:52:02,970 [INFO] === 系统检查完成 ===
2025-07-28 22:52:02,970 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:52:13,208 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:52:13,208 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:52:13,208 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:52:13,542 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:52:13,542 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:52:13,542 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:52:14,273 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:52:14,544 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:52:14,545 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:52:14,545 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:52:14,650 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:52:14,666 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:52:16,241 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:55:18 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:55:18 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:55:18 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:55:18 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:55:18 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:55:18 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:55:18 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:55:18 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:55:18 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:55:18 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:55:18 [INFO] ============================================================
2025-07-28 22:55:18 [INFO] 📋 智能错误分析报告
2025-07-28 22:55:18,261 [INFO] ============================================================
2025-07-28 22:55:18,261 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:55:18,261 [INFO] ⚠️  严重程度: 中
2025-07-28 22:55:18,261 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:55:18,261 [INFO] 💡 解决方案:
2025-07-28 22:55:18,262 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:55:18,262 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:55:18,262 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:55:18,262 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:55:18,262 [INFO] ============================================================
2025-07-28 22:55:18,262 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:55:18,263 [INFO] ============================================================
2025-07-28 22:55:18,263 [INFO] 📋 智能错误分析报告
2025-07-28 22:55:18,263 [INFO] ============================================================
2025-07-28 22:55:18,264 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:55:18,264 [INFO] ⚠️  严重程度: 低
2025-07-28 22:55:18,264 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:55:18,264 [INFO] 💡 解决方案:
2025-07-28 22:55:18,265 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:55:18,265 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:55:18,265 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:55:18,265 [INFO] ============================================================
2025-07-28 22:55:18,265 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:55:20,093 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:55:20,094 [INFO] Python版本: 3.10.16
2025-07-28 22:55:20,094 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-28 22:55:20,094 [INFO] ✅ CUDA可用: 是
2025-07-28 22:55:20,094 [INFO] CUDA版本: 12.8
2025-07-28 22:55:20,095 [INFO] GPU数量: 1
2025-07-28 22:55:20,095 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:55:20,095 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-28 22:55:20,095 [INFO] === 系统检查完成 ===
2025-07-28 22:55:20,097 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:55:31,920 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:55:31,920 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:55:31,920 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:55:32,342 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:55:32,342 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:55:32,342 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:55:33,156 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:55:33,433 [INFO] ⚠️ 端口 7866 已被占用，尝试下一个...
2025-07-28 22:55:33,433 [INFO] 🔍 找到可用端口: 7867
2025-07-28 22:55:33,433 [INFO] 🔄 端口 7866 被占用，自动切换到端口 7867
2025-07-28 22:55:33,433 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:55:33,433 [INFO] 🌐 访问地址: http://127.0.0.1:7867
2025-07-28 22:55:33,528 [INFO] HTTP Request: GET http://127.0.0.1:7867/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:55:33,557 [INFO] HTTP Request: HEAD http://127.0.0.1:7867/ "HTTP/1.1 200 OK"
2025-07-28 22:55:34,225 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 22:56:29 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 22:56:29 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 22:56:29 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 22:56:29 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 22:56:29 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 22:56:29 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 22:56:29 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 22:56:29 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 22:56:29 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 22:56:29 [INFO] 🔍 智能日志监控已启动
2025-07-28 22:56:29 [INFO] ============================================================
2025-07-28 22:56:29 [INFO] 📋 智能错误分析报告
2025-07-28 22:56:29,109 [INFO] ============================================================
2025-07-28 22:56:29,109 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 22:56:29,110 [INFO] ⚠️  严重程度: 中
2025-07-28 22:56:29,110 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 22:56:29,110 [INFO] 💡 解决方案:
2025-07-28 22:56:29,110 [INFO]    1. 以管理员身份运行程序
2025-07-28 22:56:29,110 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 22:56:29,110 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 22:56:29,110 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 22:56:29,110 [INFO] ============================================================
2025-07-28 22:56:29,111 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 22:56:29,111 [INFO] ============================================================
2025-07-28 22:56:29,111 [INFO] 📋 智能错误分析报告
2025-07-28 22:56:29,111 [INFO] ============================================================
2025-07-28 22:56:29,111 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 22:56:29,111 [INFO] ⚠️  严重程度: 低
2025-07-28 22:56:29,112 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 22:56:29,112 [INFO] 💡 解决方案:
2025-07-28 22:56:29,112 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 22:56:29,112 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 22:56:29,112 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 22:56:29,112 [INFO] ============================================================
2025-07-28 22:56:29,112 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 22:56:30,952 [INFO] === IndexTTS 系统检查 ===
2025-07-28 22:56:30,952 [INFO] Python版本: 3.10.16
2025-07-28 22:56:30,952 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-28 22:56:30,952 [INFO] ✅ CUDA可用: 是
2025-07-28 22:56:30,952 [INFO] CUDA版本: 12.8
2025-07-28 22:56:30,952 [INFO] GPU数量: 1
2025-07-28 22:56:30,952 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 22:56:30,952 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-28 22:56:30,952 [INFO] === 系统检查完成 ===
2025-07-28 22:56:30,952 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 22:56:41,584 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 22:56:41,584 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 22:56:41,584 [INFO] skip building fst for zh_normalizer ...
2025-07-28 22:56:41,952 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 22:56:41,952 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 22:56:41,952 [INFO] skip building fst for en_normalizer ...
2025-07-28 22:56:42,726 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 22:56:42,984 [INFO] 🔍 找到可用端口: 7866
2025-07-28 22:56:42,985 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 22:56:42,985 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 22:56:43,076 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 22:56:43,115 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 22:56:43,941 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 23:05:57 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 23:05:57 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 23:05:57 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 23:05:57 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 23:05:57 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 23:05:57 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 23:05:57 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 23:05:57 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 23:05:57 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 23:05:57 [INFO] 🔍 智能日志监控已启动
2025-07-28 23:05:57 [INFO] ============================================================
2025-07-28 23:05:57 [INFO] 📋 智能错误分析报告
2025-07-28 23:05:57,805 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 23:05:57,805 [INFO] ⚠️  严重程度: 中
2025-07-28 23:05:57,805 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 23:05:57,805 [INFO] 💡 解决方案:
2025-07-28 23:05:57,806 [INFO]    1. 以管理员身份运行程序
2025-07-28 23:05:57,806 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 23:05:57,806 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 23:05:57,806 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 23:05:57,807 [INFO] ============================================================
2025-07-28 23:05:57,807 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 23:05:57,807 [INFO] ============================================================
2025-07-28 23:05:57,807 [INFO] 📋 智能错误分析报告
2025-07-28 23:05:57,808 [INFO] ============================================================
2025-07-28 23:05:57,808 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 23:05:57,808 [INFO] ⚠️  严重程度: 低
2025-07-28 23:05:57,808 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 23:05:57,808 [INFO] 💡 解决方案:
2025-07-28 23:05:57,808 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 23:05:57,809 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 23:05:57,809 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 23:05:57,809 [INFO] ============================================================
2025-07-28 23:05:57,809 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 23:06:00,802 [INFO] === IndexTTS 系统检查 ===
2025-07-28 23:06:00,802 [INFO] Python版本: 3.10.0
2025-07-28 23:06:00,802 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-28 23:06:00,802 [INFO] ✅ CUDA可用: 是
2025-07-28 23:06:00,803 [INFO] CUDA版本: 12.8
2025-07-28 23:06:00,803 [INFO] GPU数量: 1
2025-07-28 23:06:00,803 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 23:06:00,803 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-28 23:06:00,803 [INFO] === 系统检查完成 ===
2025-07-28 23:06:00,804 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 23:06:14,283 [ERROR] 模型初始化失败: No module named 'tn'
2025-07-28 23:06:14,284 [INFO] 🔧 尝试使用CPU模式初始化...
2025-07-28 23:06:21,820 [ERROR] CPU模式初始化也失败: No module named 'tn'
2025-07-28 23:06:21,820 [INFO] 💡 可能的解决方案:
2025-07-28 23:06:21,820 [INFO] 1. 检查模型文件是否完整
2025-07-28 23:06:21,820 [INFO] 2. 检查CUDA环境是否正确安装
2025-07-28 23:06:21,820 [INFO] 3. 尝试重新下载模型文件
2025-07-28 23:06:21,820 [INFO] 4. 检查Python环境和依赖
2025-07-28 23:06:21,820 [INFO] 5. 对于50系显卡，建议使用最新版本的PyTorch
2025-07-28 23:06:21,820 [ERROR] 无法初始化TTS模型: No module named 'tn'
2025-07-28 23:13:15 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 23:13:15 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 23:13:15 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 23:13:15 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 23:13:15 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 23:13:15 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 23:13:15 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 23:13:15 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 23:13:15 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 23:13:15 [INFO] 🔍 智能日志监控已启动
2025-07-28 23:13:15 [INFO] ============================================================
2025-07-28 23:13:15,788 [INFO] ============================================================
2025-07-28 23:13:15,788 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 23:13:15,788 [INFO] ⚠️  严重程度: 中
2025-07-28 23:13:15,788 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 23:13:15,788 [INFO] 💡 解决方案:
2025-07-28 23:13:15,789 [INFO]    1. 以管理员身份运行程序
2025-07-28 23:13:15,789 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 23:13:15,789 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 23:13:15,789 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 23:13:15,789 [INFO] ============================================================
2025-07-28 23:13:15,790 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 23:13:15,790 [INFO] ============================================================
2025-07-28 23:13:15,790 [INFO] 📋 智能错误分析报告
2025-07-28 23:13:15,790 [INFO] ============================================================
2025-07-28 23:13:15,791 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 23:13:15,792 [INFO] ⚠️  严重程度: 低
2025-07-28 23:13:15,792 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 23:13:15,792 [INFO] 💡 解决方案:
2025-07-28 23:13:15,792 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 23:13:15,792 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 23:13:15,792 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 23:13:15,792 [INFO] ============================================================
2025-07-28 23:13:15,792 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 23:13:17,625 [INFO] === IndexTTS 系统检查 ===
2025-07-28 23:13:17,625 [INFO] Python版本: 3.10.16
2025-07-28 23:13:17,625 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-28 23:13:17,625 [INFO] ✅ CUDA可用: 是
2025-07-28 23:13:17,625 [INFO] CUDA版本: 12.8
2025-07-28 23:13:17,625 [INFO] GPU数量: 1
2025-07-28 23:13:17,625 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 23:13:17,625 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-28 23:13:17,625 [INFO] === 系统检查完成 ===
2025-07-28 23:13:17,641 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 23:13:28,819 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-28 23:13:28,819 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-28 23:13:28,819 [INFO] skip building fst for zh_normalizer ...
2025-07-28 23:13:29,196 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-28 23:13:29,196 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-28 23:13:29,196 [INFO] skip building fst for en_normalizer ...
2025-07-28 23:13:30,021 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-28 23:13:30,285 [INFO] 🔍 找到可用端口: 7866
2025-07-28 23:13:30,285 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-28 23:13:30,285 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-28 23:13:30,383 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-28 23:13:30,398 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-28 23:13:31,753 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-28 23:13:42,595 [INFO] ✅ 已删除样音: 抖音-记录美好生活_202572815380.mp3 (大小: 3.8 MB)
2025-07-28 23:13:56,677 [INFO] 开始生成语音 - 模式: 普通推理, 文本长度: 25
2025-07-28 23:14:03,224 [INFO] 语音生成成功: outputs\spk_1753715636.wav
2025-07-28 23:15:59,573 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 320
2025-07-28 23:16:32,849 [INFO] 语音生成成功: outputs\spk_1753715759.wav
2025-07-28 23:18:56,104 [INFO] ✅ 已删除样音: 抖音-记录美好生活_202572815380.mp3 (大小: 3.8 MB)
2025-07-28 23:18:58,781 [INFO] ✅ 缓存清理完成: 4个文件，释放空间 2.4 MB
2025-07-28 23:19:40,756 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 262
2025-07-28 23:20:20,856 [INFO] 语音生成成功: outputs\spk_1753715980.wav
2025-07-28 23:21:08,047 [INFO] ✅ 缓存清理完成: 2个文件，释放空间 3.2 MB
2025-07-28 23:21:21,654 [INFO] ✅ 已删除样音: 参考夜凯讲故事 - 指路人.mp3 (大小: 3.8 MB)
2025-07-28 23:55:25 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-28 23:55:25 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-28 23:55:25 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-28 23:55:25 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-28 23:55:25 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-28 23:55:25 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-28 23:55:25 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-28 23:55:25 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-28 23:55:25 [INFO] 🔍 启动智能日志监控系统...
2025-07-28 23:55:25 [INFO] 🔍 智能日志监控已启动
2025-07-28 23:55:25 [INFO] ============================================================
2025-07-28 23:55:25 [INFO] 📋 智能错误分析报告
2025-07-28 23:55:25,435 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-28 23:55:25,435 [INFO] ⚠️  严重程度: 中
2025-07-28 23:55:25,436 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-28 23:55:25,436 [INFO] 💡 解决方案:
2025-07-28 23:55:25,436 [INFO]    1. 以管理员身份运行程序
2025-07-28 23:55:25,436 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-28 23:55:25,437 [INFO]    3. 检查文件属性，取消只读设置
2025-07-28 23:55:25,437 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-28 23:55:25,437 [INFO] ============================================================
2025-07-28 23:55:25,437 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-28 23:55:25,437 [INFO] ============================================================
2025-07-28 23:55:25,437 [INFO] 📋 智能错误分析报告
2025-07-28 23:55:25,437 [INFO] ============================================================
2025-07-28 23:55:25,438 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-28 23:55:25,438 [INFO] ⚠️  严重程度: 低
2025-07-28 23:55:25,438 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-28 23:55:25,438 [INFO] 💡 解决方案:
2025-07-28 23:55:25,438 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-28 23:55:25,438 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-28 23:55:25,438 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-28 23:55:25,438 [INFO] ============================================================
2025-07-28 23:55:25,439 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-28 23:55:27,389 [INFO] === IndexTTS 系统检查 ===
2025-07-28 23:55:27,389 [INFO] Python版本: 3.10.0
2025-07-28 23:55:27,389 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-28 23:55:27,389 [INFO] ✅ CUDA可用: 是
2025-07-28 23:55:27,389 [INFO] CUDA版本: 12.8
2025-07-28 23:55:27,391 [INFO] GPU数量: 1
2025-07-28 23:55:27,391 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-28 23:55:27,391 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-28 23:55:27,391 [INFO] === 系统检查完成 ===
2025-07-28 23:55:27,393 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-28 23:55:39,568 [ERROR] 模型初始化失败: No module named 'tn'
2025-07-28 23:55:39,569 [INFO] 🔧 尝试使用CPU模式初始化...
2025-07-28 23:55:47,693 [ERROR] CPU模式初始化也失败: No module named 'tn'
2025-07-28 23:55:47,693 [INFO] 💡 可能的解决方案:
2025-07-28 23:55:47,693 [INFO] 1. 检查模型文件是否完整
2025-07-28 23:55:47,693 [INFO] 2. 检查CUDA环境是否正确安装
2025-07-28 23:55:47,693 [INFO] 3. 尝试重新下载模型文件
2025-07-28 23:55:47,693 [INFO] 4. 检查Python环境和依赖
2025-07-28 23:55:47,694 [INFO] 5. 对于50系显卡，建议使用最新版本的PyTorch
2025-07-28 23:55:47,694 [ERROR] 无法初始化TTS模型: No module named 'tn'
2025-07-29 00:03:58 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 00:03:58 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 00:03:58 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 00:03:58 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 00:03:58 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 00:03:58 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 00:03:58 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 00:03:58 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 00:03:58 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 00:03:58 [INFO] 🔍 智能日志监控已启动
2025-07-29 00:03:58 [INFO] ============================================================
2025-07-29 00:03:58,853 [INFO] ============================================================
2025-07-29 00:03:58,854 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 00:03:58,854 [INFO] ⚠️  严重程度: 中
2025-07-29 00:03:58,854 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 00:03:58,854 [INFO] 💡 解决方案:
2025-07-29 00:03:58,855 [INFO]    1. 以管理员身份运行程序
2025-07-29 00:03:58,855 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 00:03:58,855 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 00:03:58,855 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 00:03:58,856 [INFO] ============================================================
2025-07-29 00:03:58,856 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 00:03:58,856 [INFO] ============================================================
2025-07-29 00:03:58,856 [INFO] 📋 智能错误分析报告
2025-07-29 00:03:58,857 [INFO] ============================================================
2025-07-29 00:03:58,857 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 00:03:58,857 [INFO] ⚠️  严重程度: 低
2025-07-29 00:03:58,857 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 00:03:58,857 [INFO] 💡 解决方案:
2025-07-29 00:03:58,857 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 00:03:58,857 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 00:03:58,857 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 00:03:58,857 [INFO] ============================================================
2025-07-29 00:03:58,858 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 00:04:00,708 [INFO] === IndexTTS 系统检查 ===
2025-07-29 00:04:00,708 [INFO] Python版本: 3.10.0
2025-07-29 00:04:00,708 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 00:04:00,708 [INFO] ✅ CUDA可用: 是
2025-07-29 00:04:00,708 [INFO] CUDA版本: 12.8
2025-07-29 00:04:00,708 [INFO] GPU数量: 1
2025-07-29 00:04:00,708 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 00:04:00,709 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-29 00:04:00,709 [INFO] === 系统检查完成 ===
2025-07-29 00:04:00,711 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 00:04:12,359 [ERROR] 模型初始化失败: No module named 'tn'
2025-07-29 00:04:12,360 [INFO] 🔧 尝试使用CPU模式初始化...
2025-07-29 00:04:20,467 [ERROR] CPU模式初始化也失败: No module named 'tn'
2025-07-29 00:04:20,467 [INFO] 💡 可能的解决方案:
2025-07-29 00:04:20,468 [INFO] 1. 检查模型文件是否完整
2025-07-29 00:04:20,468 [INFO] 2. 检查CUDA环境是否正确安装
2025-07-29 00:04:20,468 [INFO] 3. 尝试重新下载模型文件
2025-07-29 00:04:20,468 [INFO] 4. 检查Python环境和依赖
2025-07-29 00:04:20,468 [INFO] 5. 对于50系显卡，建议使用最新版本的PyTorch
2025-07-29 00:04:20,468 [ERROR] 无法初始化TTS模型: No module named 'tn'
2025-07-29 00:07:48 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 00:07:48 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 00:07:48 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 00:07:48 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 00:07:48 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 00:07:48 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 00:07:48 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 00:07:48 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 00:07:48 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 00:07:48 [INFO] 🔍 智能日志监控已启动
2025-07-29 00:07:48 [INFO] ============================================================
2025-07-29 00:07:48 [INFO] 📋 智能错误分析报告
2025-07-29 00:07:48,757 [INFO] ============================================================
2025-07-29 00:07:48,757 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 00:07:48,757 [INFO] ⚠️  严重程度: 中
2025-07-29 00:07:48,767 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 00:07:48,767 [INFO] 💡 解决方案:
2025-07-29 00:07:48,767 [INFO]    1. 以管理员身份运行程序
2025-07-29 00:07:48,767 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 00:07:48,767 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 00:07:48,767 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 00:07:48,767 [INFO] ============================================================
2025-07-29 00:07:48,767 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 00:07:48,767 [INFO] ============================================================
2025-07-29 00:07:48,767 [INFO] 📋 智能错误分析报告
2025-07-29 00:07:48,767 [INFO] ============================================================
2025-07-29 00:07:48,767 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 00:07:48,767 [INFO] ⚠️  严重程度: 低
2025-07-29 00:07:48,767 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 00:07:48,767 [INFO] 💡 解决方案:
2025-07-29 00:07:48,767 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 00:07:48,767 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 00:07:48,770 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 00:07:48,770 [INFO] ============================================================
2025-07-29 00:07:48,770 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 00:07:50,618 [INFO] === IndexTTS 系统检查 ===
2025-07-29 00:07:50,619 [INFO] Python版本: 3.10.16
2025-07-29 00:07:50,619 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 00:07:50,619 [INFO] ✅ CUDA可用: 是
2025-07-29 00:07:50,619 [INFO] CUDA版本: 12.8
2025-07-29 00:07:50,619 [INFO] GPU数量: 1
2025-07-29 00:07:50,619 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 00:07:50,619 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-29 00:07:50,620 [INFO] === 系统检查完成 ===
2025-07-29 00:07:50,620 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 00:08:01,424 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 00:08:01,424 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 00:08:01,424 [INFO] skip building fst for zh_normalizer ...
2025-07-29 00:08:01,777 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 00:08:01,777 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 00:08:01,777 [INFO] skip building fst for en_normalizer ...
2025-07-29 00:08:02,697 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 00:08:02,960 [INFO] 🔍 找到可用端口: 7866
2025-07-29 00:08:02,961 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 00:08:02,961 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 00:08:03,041 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 00:08:03,065 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 00:08:04,322 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 00:08:19,949 [INFO] ✅ 缓存清理完成: 1个文件，释放空间 952 B
2025-07-29 00:09:40,196 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 729
2025-07-29 00:11:14,998 [INFO] 语音生成成功: outputs\spk_1753718980.wav
2025-07-29 00:16:06,064 [INFO] ✅ 已删除样音: 参考夜凯讲故事 - 指路人.mp3 (大小: 3.8 MB)
2025-07-29 00:16:13,081 [INFO] ✅ 缓存清理完成: 2个文件，释放空间 8.5 MB
2025-07-29 00:18:59,221 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 4089
2025-07-29 00:27:12,006 [INFO] 语音生成成功: outputs\spk_1753719539.wav
2025-07-29 00:37:54,082 [INFO] ✅ 已删除样音: 参考夜凯讲故事 - 指路人.mp3 (大小: 3.8 MB)
2025-07-29 00:37:56,612 [INFO] ✅ 缓存清理完成: 2个文件，释放空间 42.3 MB
2025-07-29 00:41:51 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 00:41:51 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 00:41:51 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 00:41:51 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 00:41:51 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 00:41:51 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 00:41:51 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 00:41:51 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 00:41:51 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 00:41:51 [INFO] 🔍 智能日志监控已启动
2025-07-29 00:41:51 [INFO] ============================================================
2025-07-29 00:41:51,069 [INFO] ============================================================
2025-07-29 00:41:51,069 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 00:41:51,069 [INFO] ⚠️  严重程度: 中
2025-07-29 00:41:51,070 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 00:41:51,070 [INFO] 💡 解决方案:
2025-07-29 00:41:51,070 [INFO]    1. 以管理员身份运行程序
2025-07-29 00:41:51,070 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 00:41:51,070 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 00:41:51,070 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 00:41:51,070 [INFO] ============================================================
2025-07-29 00:41:51,071 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 00:41:51,071 [INFO] ============================================================
2025-07-29 00:41:51,071 [INFO] 📋 智能错误分析报告
2025-07-29 00:41:51,071 [INFO] ============================================================
2025-07-29 00:41:51,071 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 00:41:51,072 [INFO] ⚠️  严重程度: 低
2025-07-29 00:41:51,072 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 00:41:51,072 [INFO] 💡 解决方案:
2025-07-29 00:41:51,072 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 00:41:51,072 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 00:41:51,072 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 00:41:51,073 [INFO] ============================================================
2025-07-29 00:41:51,073 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 00:41:53,214 [INFO] === IndexTTS 系统检查 ===
2025-07-29 00:41:53,214 [INFO] Python版本: 3.10.16
2025-07-29 00:41:53,214 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 00:41:53,215 [INFO] ✅ CUDA可用: 是
2025-07-29 00:41:53,215 [INFO] CUDA版本: 12.8
2025-07-29 00:41:53,215 [INFO] GPU数量: 1
2025-07-29 00:41:53,215 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 00:41:53,215 [INFO] 磁盘空间: 总计 631 GB, 已用 294 GB, 可用 336 GB
2025-07-29 00:41:53,215 [INFO] === 系统检查完成 ===
2025-07-29 00:41:53,217 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 00:42:04,802 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 00:42:04,802 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 00:42:04,802 [INFO] skip building fst for zh_normalizer ...
2025-07-29 00:42:05,167 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 00:42:05,167 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 00:42:05,167 [INFO] skip building fst for en_normalizer ...
2025-07-29 00:42:05,881 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 00:42:06,162 [INFO] 🔍 找到可用端口: 7866
2025-07-29 00:42:06,163 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 00:42:06,163 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 00:42:06,267 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 00:42:06,283 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 01:04:03 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 01:04:03 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 01:04:03 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 01:04:03 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 01:04:03 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 01:04:03 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 01:04:03 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 01:04:03 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 01:04:03 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 01:04:03 [INFO] 🔍 智能日志监控已启动
2025-07-29 01:04:03,629 [INFO] 📋 智能错误分析报告
2025-07-29 01:04:03,629 [INFO] ============================================================
2025-07-29 01:04:03,630 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 01:04:03,630 [INFO] ⚠️  严重程度: 中
2025-07-29 01:04:03,630 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 01:04:03,631 [INFO] 💡 解决方案:
2025-07-29 01:04:03,631 [INFO]    1. 以管理员身份运行程序
2025-07-29 01:04:03,632 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 01:04:03,632 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 01:04:03,633 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 01:04:03,633 [INFO] ============================================================
2025-07-29 01:04:03,633 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 01:04:03,634 [INFO] ============================================================
2025-07-29 01:04:03,634 [INFO] 📋 智能错误分析报告
2025-07-29 01:04:03,634 [INFO] ============================================================
2025-07-29 01:04:03,634 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 01:04:03,634 [INFO] ⚠️  严重程度: 低
2025-07-29 01:04:03,634 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 01:04:03,635 [INFO] 💡 解决方案:
2025-07-29 01:04:03,635 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 01:04:03,635 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 01:04:03,635 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 01:04:03,635 [INFO] ============================================================
2025-07-29 01:04:03,636 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 01:04:05,720 [INFO] === IndexTTS 系统检查 ===
2025-07-29 01:04:05,720 [INFO] Python版本: 3.10.16
2025-07-29 01:04:05,720 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 01:04:05,720 [INFO] ✅ CUDA可用: 是
2025-07-29 01:04:05,721 [INFO] CUDA版本: 12.8
2025-07-29 01:04:05,721 [INFO] GPU数量: 1
2025-07-29 01:04:05,721 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 01:04:05,721 [INFO] 磁盘空间: 总计 631 GB, 已用 303 GB, 可用 327 GB
2025-07-29 01:04:05,721 [INFO] === 系统检查完成 ===
2025-07-29 01:04:05,723 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 01:04:17,785 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 01:04:17,785 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 01:04:17,785 [INFO] skip building fst for zh_normalizer ...
2025-07-29 01:04:18,255 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 01:04:18,255 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 01:04:18,255 [INFO] skip building fst for en_normalizer ...
2025-07-29 01:04:19,024 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 01:04:19,299 [INFO] 🔍 找到可用端口: 7866
2025-07-29 01:04:19,299 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 01:04:19,299 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 01:04:19,413 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 01:04:19,435 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 01:04:19,880 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 01:04:45,113 [INFO] 开始生成语音 - 模式: 普通推理, 文本长度: 61
2025-07-29 01:05:03,193 [INFO] 语音生成成功: outputs\spk_1753722285.wav
2025-07-29 11:17:49 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 11:17:49 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 11:17:49 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 11:17:49 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 11:17:49 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 11:17:49 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 11:17:49 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 11:17:49 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 11:17:49 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 11:17:49 [INFO] 🔍 智能日志监控已启动
2025-07-29 11:17:49,087 [INFO] ============================================================
2025-07-29 11:17:49,087 [INFO] 📋 智能错误分析报告
2025-07-29 11:17:49,087 [INFO] ============================================================
2025-07-29 11:17:49,088 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 11:17:49,088 [INFO] ⚠️  严重程度: 中
2025-07-29 11:17:49,088 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 11:17:49,089 [INFO] 💡 解决方案:
2025-07-29 11:17:49,090 [INFO]    1. 以管理员身份运行程序
2025-07-29 11:17:49,090 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 11:17:49,090 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 11:17:49,090 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 11:17:49,090 [INFO] ============================================================
2025-07-29 11:17:49,090 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 11:17:49,090 [INFO] ============================================================
2025-07-29 11:17:49,091 [INFO] 📋 智能错误分析报告
2025-07-29 11:17:49,091 [INFO] ============================================================
2025-07-29 11:17:49,091 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 11:17:49,091 [INFO] ⚠️  严重程度: 低
2025-07-29 11:17:49,091 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 11:17:49,092 [INFO] 💡 解决方案:
2025-07-29 11:17:49,092 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 11:17:49,092 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 11:17:49,092 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 11:17:49,092 [INFO] ============================================================
2025-07-29 11:17:49,092 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 11:17:51,162 [INFO] === IndexTTS 系统检查 ===
2025-07-29 11:17:51,162 [INFO] Python版本: 3.10.16
2025-07-29 11:17:51,163 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 11:17:51,163 [INFO] ✅ CUDA可用: 是
2025-07-29 11:17:51,163 [INFO] CUDA版本: 12.8
2025-07-29 11:17:51,163 [INFO] GPU数量: 1
2025-07-29 11:17:51,163 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 11:17:51,163 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 11:17:51,163 [INFO] === 系统检查完成 ===
2025-07-29 11:17:51,166 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 11:18:02,592 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 11:18:02,592 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 11:18:02,593 [INFO] skip building fst for zh_normalizer ...
2025-07-29 11:18:02,928 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 11:18:02,928 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 11:18:02,929 [INFO] skip building fst for en_normalizer ...
2025-07-29 11:18:03,610 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 11:18:03,884 [INFO] 🔍 找到可用端口: 7866
2025-07-29 11:18:03,885 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 11:18:03,885 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 11:18:03,981 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 11:18:03,999 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 11:18:04,494 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 11:18:44 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 11:18:44 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 11:18:44 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 11:18:44 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 11:18:44 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 11:18:44 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 11:18:44 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 11:18:44 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 11:18:44 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 11:18:44 [INFO] 🔍 智能日志监控已启动
2025-07-29 11:18:44,754 [INFO] 📋 智能错误分析报告
2025-07-29 11:18:44,755 [INFO] ============================================================
2025-07-29 11:18:44,755 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 11:18:44,755 [INFO] ⚠️  严重程度: 中
2025-07-29 11:18:44,756 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 11:18:44,756 [INFO] 💡 解决方案:
2025-07-29 11:18:44,756 [INFO]    1. 以管理员身份运行程序
2025-07-29 11:18:44,756 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 11:18:44,756 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 11:18:44,756 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 11:18:44,757 [INFO] ============================================================
2025-07-29 11:18:44,757 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 11:18:44,757 [INFO] ============================================================
2025-07-29 11:18:44,757 [INFO] 📋 智能错误分析报告
2025-07-29 11:18:44,758 [INFO] ============================================================
2025-07-29 11:18:44,758 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 11:18:44,758 [INFO] ⚠️  严重程度: 低
2025-07-29 11:18:44,759 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 11:18:44,759 [INFO] 💡 解决方案:
2025-07-29 11:18:44,759 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 11:18:44,759 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 11:18:44,759 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 11:18:44,760 [INFO] ============================================================
2025-07-29 11:18:44,760 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 11:18:46,567 [INFO] === IndexTTS 系统检查 ===
2025-07-29 11:18:46,567 [INFO] Python版本: 3.10.16
2025-07-29 11:18:46,568 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 11:18:46,568 [INFO] ✅ CUDA可用: 是
2025-07-29 11:18:46,568 [INFO] CUDA版本: 12.8
2025-07-29 11:18:46,568 [INFO] GPU数量: 1
2025-07-29 11:18:46,569 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 11:18:46,569 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 11:18:46,569 [INFO] === 系统检查完成 ===
2025-07-29 11:18:46,571 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 11:18:57,120 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 11:18:57,121 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 11:18:57,121 [INFO] skip building fst for zh_normalizer ...
2025-07-29 11:18:57,449 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 11:18:57,450 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 11:18:57,450 [INFO] skip building fst for en_normalizer ...
2025-07-29 11:18:58,114 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 11:18:58,364 [INFO] 🔍 找到可用端口: 7866
2025-07-29 11:18:58,364 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 11:18:58,365 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 11:18:58,454 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 11:18:58,472 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 11:18:59,017 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 11:19:07 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 11:19:07 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 11:19:07 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 11:19:07 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 11:19:07 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 11:19:07 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 11:19:07 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 11:19:07 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 11:19:07 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 11:19:07 [INFO] 🔍 智能日志监控已启动
2025-07-29 11:19:07,785 [INFO] ============================================================
2025-07-29 11:19:07,785 [INFO] 📋 智能错误分析报告
2025-07-29 11:19:07,785 [INFO] ============================================================
2025-07-29 11:19:07,785 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 11:19:07,785 [INFO] ⚠️  严重程度: 中
2025-07-29 11:19:07,786 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 11:19:07,786 [INFO] 💡 解决方案:
2025-07-29 11:19:07,786 [INFO]    1. 以管理员身份运行程序
2025-07-29 11:19:07,786 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 11:19:07,786 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 11:19:07,787 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 11:19:07,787 [INFO] ============================================================
2025-07-29 11:19:07,787 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 11:19:07,787 [INFO] ============================================================
2025-07-29 11:19:07,787 [INFO] 📋 智能错误分析报告
2025-07-29 11:19:07,787 [INFO] ============================================================
2025-07-29 11:19:07,788 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 11:19:07,788 [INFO] ⚠️  严重程度: 低
2025-07-29 11:19:07,788 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 11:19:07,788 [INFO] 💡 解决方案:
2025-07-29 11:19:07,788 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 11:19:07,788 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 11:19:07,789 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 11:19:07,789 [INFO] ============================================================
2025-07-29 11:19:07,789 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 11:19:09,700 [INFO] === IndexTTS 系统检查 ===
2025-07-29 11:19:09,700 [INFO] Python版本: 3.10.16
2025-07-29 11:19:09,700 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 11:19:09,701 [INFO] ✅ CUDA可用: 是
2025-07-29 11:19:09,701 [INFO] CUDA版本: 12.8
2025-07-29 11:19:09,701 [INFO] GPU数量: 1
2025-07-29 11:19:09,701 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 11:19:09,701 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 11:19:09,702 [INFO] === 系统检查完成 ===
2025-07-29 11:19:09,703 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 11:19:20,430 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 11:19:20,431 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 11:19:20,431 [INFO] skip building fst for zh_normalizer ...
2025-07-29 11:19:20,801 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 11:19:20,801 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 11:19:20,802 [INFO] skip building fst for en_normalizer ...
2025-07-29 11:19:21,574 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 11:19:21,833 [INFO] 🔍 找到可用端口: 7866
2025-07-29 11:19:21,833 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 11:19:21,833 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 11:19:21,935 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 11:19:21,951 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 11:19:22,476 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 11:30:44,782 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 314
2025-07-29 11:31:12,057 [INFO] 语音生成成功: outputs\spk_1753759844.wav
2025-07-29 11:33:26,336 [INFO] ✅ 缓存清理完成: 2个文件，释放空间 2.7 MB
2025-07-29 11:33:27,625 [INFO] 开始生成语音 - 模式: 批次推理, 文本长度: 314
2025-07-29 11:33:51,539 [INFO] 语音生成成功: outputs\spk_1753760007.wav
2025-07-29 12:03:29 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 12:03:29 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 12:03:29 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 12:03:29 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 12:03:29 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 12:03:29 [ERROR] ❌ 50系显卡兼容性检查异常: name 'smart_logger' is not defined
2025-07-29 12:03:29 [INFO] 💡 程序将继续启动，但可能存在兼容性问题
2025-07-29 12:03:29 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 12:08:09 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 12:08:09 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 12:08:09 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 12:08:09 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 12:08:09 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 12:08:09 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 12:08:09 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 12:08:09 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 12:08:09 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 12:08:09 [INFO] 🔍 智能日志监控已启动
2025-07-29 12:08:09 [INFO] ============================================================
2025-07-29 12:08:09,229 [INFO] 📋 智能错误分析报告
2025-07-29 12:08:09,229 [INFO] ============================================================
2025-07-29 12:08:09,230 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 12:08:09,230 [INFO] ⚠️  严重程度: 中
2025-07-29 12:08:09,230 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 12:08:09,230 [INFO] 💡 解决方案:
2025-07-29 12:08:09,230 [INFO]    1. 以管理员身份运行程序
2025-07-29 12:08:09,230 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 12:08:09,231 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 12:08:09,231 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 12:08:09,231 [INFO] ============================================================
2025-07-29 12:08:09,231 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 12:08:09,231 [INFO] ============================================================
2025-07-29 12:08:09,232 [INFO] 📋 智能错误分析报告
2025-07-29 12:08:09,232 [INFO] ============================================================
2025-07-29 12:08:09,232 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 12:08:09,232 [INFO] ⚠️  严重程度: 低
2025-07-29 12:08:09,232 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 12:08:09,233 [INFO] 💡 解决方案:
2025-07-29 12:08:09,233 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 12:08:09,233 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 12:08:09,233 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 12:08:09,233 [INFO] ============================================================
2025-07-29 12:08:09,233 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 12:08:11,105 [INFO] === IndexTTS 系统检查 ===
2025-07-29 12:08:11,105 [INFO] Python版本: 3.10.16
2025-07-29 12:08:11,105 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 12:08:11,105 [INFO] ✅ CUDA可用: 是
2025-07-29 12:08:11,105 [INFO] CUDA版本: 12.8
2025-07-29 12:08:11,105 [INFO] GPU数量: 1
2025-07-29 12:08:11,106 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 12:08:11,106 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 12:08:11,106 [INFO] === 系统检查完成 ===
2025-07-29 12:08:11,108 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 12:08:21,810 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_tagger.fst
2025-07-29 12:08:21,810 [INFO]                     D:\AIGC\AI_Video\Index-TTS\indextts\utils\tagger_cache\zh_tn_verbalizer.fst
2025-07-29 12:08:21,810 [INFO] skip building fst for zh_normalizer ...
2025-07-29 12:08:22,177 [INFO] found existing fst: D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_tagger.fst
2025-07-29 12:08:22,177 [INFO]                     D:\AIGC\AI_Video\Index-TTS\coderyuanai\lib\site-packages\tn\en_tn_verbalizer.fst
2025-07-29 12:08:22,178 [INFO] skip building fst for en_normalizer ...
2025-07-29 12:08:22,913 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 12:08:23,186 [INFO] 🔍 找到可用端口: 7866
2025-07-29 12:08:23,186 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 12:08:23,187 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 12:08:23,286 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 12:08:23,305 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 12:08:23,757 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-29 12:17:17 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 12:17:17 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 12:17:17 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 12:17:17 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 12:17:17 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 12:17:17 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 12:17:17 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 12:17:17 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 12:17:17 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 12:17:17 [INFO] 🔍 智能日志监控已启动
2025-07-29 12:17:17 [INFO] ============================================================
2025-07-29 12:17:17 [INFO] 📋 智能错误分析报告
2025-07-29 12:17:17 [INFO] ============================================================
2025-07-29 12:17:17,611 [INFO] ⚠️  严重程度: 中
2025-07-29 12:17:17,611 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 12:17:17,611 [INFO] 💡 解决方案:
2025-07-29 12:17:17,611 [INFO]    1. 以管理员身份运行程序
2025-07-29 12:17:17,611 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 12:17:17,611 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 12:17:17,612 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 12:17:17,612 [INFO] ============================================================
2025-07-29 12:17:17,612 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 12:17:17,612 [INFO] ============================================================
2025-07-29 12:17:17,613 [INFO] 📋 智能错误分析报告
2025-07-29 12:17:17,613 [INFO] ============================================================
2025-07-29 12:17:17,613 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 12:17:17,613 [INFO] ⚠️  严重程度: 低
2025-07-29 12:17:17,613 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 12:17:17,614 [INFO] 💡 解决方案:
2025-07-29 12:17:17,614 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 12:17:17,614 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 12:17:17,614 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 12:17:17,614 [INFO] ============================================================
2025-07-29 12:17:17,614 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 12:17:19,393 [INFO] === IndexTTS 系统检查 ===
2025-07-29 12:17:19,393 [INFO] Python版本: 3.10.0
2025-07-29 12:17:19,393 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 12:17:19,393 [INFO] ✅ CUDA可用: 是
2025-07-29 12:17:19,393 [INFO] CUDA版本: 12.8
2025-07-29 12:17:19,393 [INFO] GPU数量: 1
2025-07-29 12:17:19,393 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 12:17:19,393 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 12:17:19,393 [INFO] === 系统检查完成 ===
2025-07-29 12:17:19,395 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 12:17:32,690 [ERROR] 模型初始化失败: No module named 'tn'
2025-07-29 12:17:32,691 [INFO] 🔧 尝试使用CPU模式初始化...
2025-07-29 12:17:40,289 [ERROR] CPU模式初始化也失败: No module named 'tn'
2025-07-29 12:17:40,289 [INFO] 💡 可能的解决方案:
2025-07-29 12:17:40,289 [INFO] 1. 检查模型文件是否完整
2025-07-29 12:17:40,289 [INFO] 2. 检查CUDA环境是否正确安装
2025-07-29 12:17:40,290 [INFO] 3. 尝试重新下载模型文件
2025-07-29 12:17:40,290 [INFO] 4. 检查Python环境和依赖
2025-07-29 12:17:40,290 [INFO] 5. 对于50系显卡，建议使用最新版本的PyTorch
2025-07-29 12:17:40,290 [ERROR] 无法初始化TTS模型: No module named 'tn'
2025-07-29 12:20:50 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 12:20:50 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 12:20:50 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 12:20:50 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 12:20:50 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 12:20:50 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 12:20:50 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 12:20:50 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 12:20:50 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 12:20:50 [INFO] 🔍 智能日志监控已启动
2025-07-29 12:20:50 [INFO] ============================================================
2025-07-29 12:20:50,617 [INFO] 📋 智能错误分析报告
2025-07-29 12:20:50,617 [INFO] ============================================================
2025-07-29 12:20:50,617 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 12:20:50,617 [INFO] ⚠️  严重程度: 中
2025-07-29 12:20:50,617 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 12:20:50,617 [INFO] 💡 解决方案:
2025-07-29 12:20:50,617 [INFO]    1. 以管理员身份运行程序
2025-07-29 12:20:50,617 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 12:20:50,617 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 12:20:50,617 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 12:20:50,617 [INFO] ============================================================
2025-07-29 12:20:50,617 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 12:20:50,617 [INFO] ============================================================
2025-07-29 12:20:50,627 [INFO] 📋 智能错误分析报告
2025-07-29 12:20:50,627 [INFO] ============================================================
2025-07-29 12:20:50,627 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 12:20:50,627 [INFO] ⚠️  严重程度: 低
2025-07-29 12:20:50,627 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 12:20:50,627 [INFO] 💡 解决方案:
2025-07-29 12:20:50,627 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 12:20:50,627 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 12:20:50,628 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 12:20:50,628 [INFO] ============================================================
2025-07-29 12:20:50,628 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 12:20:52,512 [INFO] === IndexTTS 系统检查 ===
2025-07-29 12:20:52,512 [INFO] Python版本: 3.10.0
2025-07-29 12:20:52,512 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 12:20:52,512 [INFO] ✅ CUDA可用: 是
2025-07-29 12:20:52,512 [INFO] CUDA版本: 12.8
2025-07-29 12:20:52,512 [INFO] GPU数量: 1
2025-07-29 12:20:52,512 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 12:20:52,512 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 12:20:52,512 [INFO] === 系统检查完成 ===
2025-07-29 12:20:52,518 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 12:21:03,665 [ERROR] 模型初始化失败: No module named 'tn'
2025-07-29 12:21:03,666 [INFO] 🔧 尝试使用CPU模式初始化...
2025-07-29 12:21:11,182 [ERROR] CPU模式初始化也失败: No module named 'tn'
2025-07-29 12:21:11,182 [INFO] 💡 可能的解决方案:
2025-07-29 12:21:11,182 [INFO] 1. 检查模型文件是否完整
2025-07-29 12:21:11,182 [INFO] 2. 检查CUDA环境是否正确安装
2025-07-29 12:21:11,182 [INFO] 3. 尝试重新下载模型文件
2025-07-29 12:21:11,182 [INFO] 4. 检查Python环境和依赖
2025-07-29 12:21:11,183 [INFO] 5. 对于50系显卡，建议使用最新版本的PyTorch
2025-07-29 12:21:11,183 [ERROR] 无法初始化TTS模型: No module named 'tn'
2025-07-29 12:22:12 [INFO] ✅ ✅ 检测到Demucs - 使用最先进的人声分离技术
2025-07-29 12:22:12 [INFO] ✅ 人声分离功能已加载 (方法: demucs)
2025-07-29 12:22:12 [INFO] 🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理
2025-07-29 12:22:12 [INFO] 🎮 开始50系显卡兼容性检查...
2025-07-29 12:22:12 [INFO] 🔍 正在检测GPU型号和CUDA兼容性...
2025-07-29 12:22:12 [INFO] 🎮 检测到GPU: NVIDIA GeForce RTX 2060
2025-07-29 12:22:12 [INFO] ✅ 非50系显卡，CUDA兼容性检查跳过
2025-07-29 12:22:12 [INFO] ✅ ✅ 50系显卡兼容性检查完成
2025-07-29 12:22:12 [INFO] 🔍 启动智能日志监控系统...
2025-07-29 12:22:12 [INFO] 🔍 智能日志监控已启动
2025-07-29 12:22:12,881 [INFO] 📋 智能错误分析报告
2025-07-29 12:22:12,881 [INFO] ============================================================
2025-07-29 12:22:12,881 [INFO] 🏷️  错误类型: 🔒 文件权限不足错误
2025-07-29 12:22:12,881 [INFO] ⚠️  严重程度: 中
2025-07-29 12:22:12,882 [INFO] 📝 中文说明: 程序没有足够的权限访问文件或目录
2025-07-29 12:22:12,882 [INFO] 💡 解决方案:
2025-07-29 12:22:12,882 [INFO]    1. 以管理员身份运行程序
2025-07-29 12:22:12,882 [INFO]    2. 关闭占用文件的其他程序（如文本编辑器）
2025-07-29 12:22:12,882 [INFO]    3. 检查文件属性，取消只读设置
2025-07-29 12:22:12,882 [INFO]    4. 修改文件夹权限，给予完全控制权限
2025-07-29 12:22:12,882 [INFO] ============================================================
2025-07-29 12:22:12,883 [INFO] 🔧 建议: 请以管理员身份重新运行程序
2025-07-29 12:22:12,883 [INFO] ============================================================
2025-07-29 12:22:12,883 [INFO] 📋 智能错误分析报告
2025-07-29 12:22:12,883 [INFO] ============================================================
2025-07-29 12:22:12,883 [INFO] 🏷️  错误类型: ⚡ BigVGAN CUDA内核加载失败
2025-07-29 12:22:12,884 [INFO] ⚠️  严重程度: 低
2025-07-29 12:22:12,884 [INFO] 📝 中文说明: BigVGAN的CUDA加速内核无法加载，但不影响功能
2025-07-29 12:22:12,884 [INFO] 💡 解决方案:
2025-07-29 12:22:12,884 [INFO]    1. 这是正常现象，程序会自动使用PyTorch后端
2025-07-29 12:22:12,884 [INFO]    2. 如需优化性能，可安装Visual Studio Build Tools
2025-07-29 12:22:12,884 [INFO]    3. 当前使用PyTorch后端，功能完全正常
2025-07-29 12:22:12,884 [INFO] ============================================================
2025-07-29 12:22:12,885 [INFO] 🔧 自动处理: 已标记为可忽略警告
2025-07-29 12:22:14,612 [INFO] === IndexTTS 系统检查 ===
2025-07-29 12:22:14,612 [INFO] Python版本: 3.10.0
2025-07-29 12:22:14,612 [INFO] PyTorch版本: 2.7.0+cu128
2025-07-29 12:22:14,612 [INFO] ✅ CUDA可用: 是
2025-07-29 12:22:14,612 [INFO] CUDA版本: 12.8
2025-07-29 12:22:14,612 [INFO] GPU数量: 1
2025-07-29 12:22:14,613 [INFO] GPU 0: NVIDIA GeForce RTX 2060 (12.0 GB)
2025-07-29 12:22:14,613 [INFO] 磁盘空间: 总计 631 GB, 已用 291 GB, 可用 339 GB
2025-07-29 12:22:14,613 [INFO] === 系统检查完成 ===
2025-07-29 12:22:14,614 [INFO] 🔄 开始: 初始化IndexTTS模型
2025-07-29 12:22:25,496 [INFO] ✅ ✅ IndexTTS模型初始化成功
2025-07-29 12:22:25,816 [INFO] 🔍 找到可用端口: 7866
2025-07-29 12:22:25,817 [INFO] 🚀 IndexTTS WebUI 启动完成
2025-07-29 12:22:25,817 [INFO] 🌐 访问地址: http://127.0.0.1:7866
2025-07-29 12:22:25,929 [INFO] HTTP Request: GET http://127.0.0.1:7866/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-07-29 12:22:26,020 [INFO] HTTP Request: HEAD http://127.0.0.1:7866/ "HTTP/1.1 200 OK"
2025-07-29 12:22:26,200 [INFO] HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
