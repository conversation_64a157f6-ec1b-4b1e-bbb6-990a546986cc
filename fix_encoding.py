#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复webui.py编码问题
"""

import os
import shutil
from datetime import datetime

def fix_encoding_issue():
    """修复编码问题"""
    print("🔧 IndexTTS 编码问题修复工具")
    print("=" * 50)
    
    source_file = "webui.py"
    
    # 创建备份
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"webui_backup_encoding_{timestamp}.py"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    print(f"💾 创建备份: {backup_file}")
    shutil.copy2(source_file, backup_file)
    
    # 尝试不同的编码方式读取文件
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']
    content = None
    original_encoding = None
    
    for encoding in encodings:
        try:
            print(f"🔍 尝试编码: {encoding}")
            with open(source_file, 'r', encoding=encoding) as f:
                content = f.read()
            original_encoding = encoding
            print(f"✅ 成功读取，原编码: {encoding}")
            break
        except UnicodeDecodeError as e:
            print(f"❌ {encoding} 失败: {e}")
        except Exception as e:
            print(f"❌ {encoding} 其他错误: {e}")
    
    if content is None:
        print("❌ 无法读取文件，所有编码都失败")
        return False
    
    # 检查问题行
    lines = content.split('\n')
    print(f"📄 文件总行数: {len(lines)}")
    
    if len(lines) > 21:
        line22 = lines[21]  # 第22行
        print(f"📝 第22行原内容: {repr(line22)}")
        
        # 检查并修复特殊字符
        problematic_chars = []
        for i, char in enumerate(line22):
            if ord(char) > 127:
                problematic_chars.append((i, char, ord(char)))
        
        if problematic_chars:
            print("🔍 发现问题字符:")
            for pos, char, code in problematic_chars:
                print(f"  位置 {pos}: {repr(char)} (U+{code:04X})")
    
    # 修复方案1: 重新编码为UTF-8
    print("\n🔧 应用修复方案...")
    
    try:
        # 确保内容是正确的UTF-8
        if isinstance(content, str):
            # 清理可能的问题字符
            content_fixed = content.encode('utf-8', errors='ignore').decode('utf-8')
        else:
            content_fixed = content
        
        # 写入修复后的文件
        with open(source_file, 'w', encoding='utf-8', newline='\n') as f:
            f.write(content_fixed)
        
        print("✅ 文件已修复并保存为UTF-8编码")
        
        # 验证修复结果
        try:
            with open(source_file, 'r', encoding='utf-8') as f:
                test_content = f.read()
            print("✅ 修复验证成功")
            
            # 检查第22行
            test_lines = test_content.split('\n')
            if len(test_lines) > 21:
                print(f"📝 修复后第22行: {repr(test_lines[21])}")
            
            return True
            
        except Exception as e:
            print(f"❌ 修复验证失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        
        # 恢复备份
        print("🔄 恢复备份文件...")
        shutil.copy2(backup_file, source_file)
        return False

def create_clean_version():
    """创建一个干净的版本，移除可能有问题的中文显示部分"""
    print("\n🧹 创建干净版本（移除中文显示）...")
    
    source_file = "webui.py"
    
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # 找到中文显示部分并注释掉
        for i, line in enumerate(lines):
            if i >= 19 and i <= 34:  # 中文显示部分大概在这个范围
                if any(keyword in line for keyword in ['print("📣', 'print("🎁', 'print("🐧', 'print("📱']):
                    lines[i] = '# ' + line  # 注释掉这些行
                    print(f"注释第{i+1}行: {line[:50]}...")
        
        # 保存修改
        with open(source_file, 'w', encoding='utf-8', newline='\n') as f:
            f.write('\n'.join(lines))
        
        print("✅ 干净版本创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建干净版本失败: {e}")
        return False

if __name__ == "__main__":
    print("选择修复方案:")
    print("1. 修复编码问题（推荐）")
    print("2. 创建干净版本（注释中文显示）")
    print("3. 两种方案都尝试")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        success = fix_encoding_issue()
    elif choice == "2":
        success = create_clean_version()
    elif choice == "3":
        success1 = fix_encoding_issue()
        if not success1:
            success = create_clean_version()
        else:
            success = success1
    else:
        print("❌ 无效选择")
        success = False
    
    if success:
        print("\n🎉 修复完成！请重新运行IndexTTS")
    else:
        print("\n❌ 修复失败，请检查错误信息")
    
    input("按回车键退出...")
