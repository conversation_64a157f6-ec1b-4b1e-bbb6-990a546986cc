#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS WebUI 启动器
通过导入方式运行webui.py，避免PyInstaller的字节码问题
"""

import sys
import os
import importlib.util

def main():
    """主函数"""
    print("🚀 IndexTTS WebUI 启动器")
    print("=" * 50)
    
    # 获取webui.py的路径
    webui_path = os.path.join(os.path.dirname(__file__), 'webui.py')
    
    if not os.path.exists(webui_path):
        print(f"❌ 找不到webui.py文件: {webui_path}")
        input("按任意键退出...")
        return
    
    try:
        print("📂 加载webui模块...")
        
        # 动态导入webui模块
        spec = importlib.util.spec_from_file_location("webui", webui_path)
        webui_module = importlib.util.module_from_spec(spec)
        
        # 将webui模块添加到sys.modules中
        sys.modules["webui"] = webui_module
        
        print("🔧 执行webui程序...")
        
        # 执行webui模块
        spec.loader.exec_module(webui_module)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按任意键退出...")

if __name__ == "__main__":
    main()
