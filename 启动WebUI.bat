@echo off
chcp 65001 >nul
title IndexTTS WebUI - 字节码保护版本

echo.
echo ========================================
echo 🚀 IndexTTS WebUI - 字节码保护版本
echo ========================================
echo.

if not exist "webui_launcher.py" (
    echo ❌ 找不到启动器: webui_launcher.py
    pause
    exit /b 1
)

if not exist "webui.pyc" (
    echo ❌ 找不到程序文件: webui.pyc
    pause
    exit /b 1
)

echo 📂 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo 🚀 启动程序...
echo.

python webui_launcher.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
)
