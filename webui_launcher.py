#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS WebUI 启动器 - 字节码版本
"""

import os
import sys
import marshal

def main():
    """启动主程序"""
    print("🚀 IndexTTS WebUI - 字节码保护版本")
    print("=" * 50)
    
    # 查找字节码文件
    bytecode_file = "webui.pyc"
    if not os.path.exists(bytecode_file):
        print(f"❌ 找不到程序文件: {bytecode_file}")
        input("按任意键退出...")
        return
    
    try:
        print("📂 加载程序...")
        
        # 读取并执行字节码
        with open(bytecode_file, 'rb') as f:
            # 跳过pyc文件头
            f.read(16)  # Python 3.7+ 的pyc文件头是16字节
            code_obj = marshal.load(f)
        
        print("🚀 启动程序...")
        exec(code_obj)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保使用原始的、未修改的程序文件")
        input("按任意键退出...")

if __name__ == "__main__":
    main()
