#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动代码保护工具
对webui.py进行基础混淆和保护
"""

import os
import base64
import zlib
import marshal
import random
import string

def obfuscate_code():
    """对代码进行基础混淆"""
    print("🔐 开始代码混淆保护...")
    
    # 读取原始代码
    with open('webui.py', 'r', encoding='utf-8') as f:
        original_code = f.read()
    
    print(f"📄 原始代码大小: {len(original_code)} 字符")
    
    # 编译代码为字节码
    try:
        compiled_code = compile(original_code, 'webui.py', 'exec')
        print("✅ 代码编译成功")
    except SyntaxError as e:
        print(f"❌ 代码编译失败: {e}")
        return False
    
    # 序列化字节码
    marshaled_code = marshal.dumps(compiled_code)
    print(f"📦 字节码大小: {len(marshaled_code)} 字节")
    
    # 压缩字节码
    compressed_code = zlib.compress(marshaled_code, 9)
    print(f"🗜️ 压缩后大小: {len(compressed_code)} 字节")
    
    # Base64编码
    encoded_code = base64.b64encode(compressed_code).decode('ascii')
    print(f"🔤 编码后大小: {len(encoded_code)} 字符")
    
    # 生成随机变量名
    var_names = [''.join(random.choices(string.ascii_letters, k=8)) for _ in range(10)]
    
    # 创建保护后的代码
    protected_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS WebUI - 受保护版本
此文件已经过混淆处理，请勿尝试修改
"""

import base64
import zlib
import marshal
import sys

# 预导入必要的模块以避免运行时错误
import os
import json
import threading
import time
import logging
import traceback
import shutil
import glob
import tempfile
import re
from datetime import datetime
from pathlib import Path
try:
    from typing import Dict, List, Optional, Tuple, Any
except ImportError:
    # 为旧版本Python提供兼容性
    Dict = dict
    List = list
    Optional = type(None)
    Tuple = tuple
    Any = object

# 受保护的代码数据
{var_names[0]} = "{encoded_code[:len(encoded_code)//3]}"
{var_names[1]} = "{encoded_code[len(encoded_code)//3:2*len(encoded_code)//3]}"
{var_names[2]} = "{encoded_code[2*len(encoded_code)//3:]}"

def {var_names[3]}():
    """解码并执行主程序"""
    try:
        # 重组编码数据
        {var_names[4]} = {var_names[0]} + {var_names[1]} + {var_names[2]}
        
        # 解码
        {var_names[5]} = base64.b64decode({var_names[4]}.encode('ascii'))
        
        # 解压缩
        {var_names[6]} = zlib.decompress({var_names[5]})
        
        # 反序列化字节码
        {var_names[7]} = marshal.loads({var_names[6]})
        
        # 执行代码
        exec({var_names[7]})
        
    except Exception as e:
        print(f"程序启动失败: {{e}}")
        print("请确保使用原始的、未修改的程序文件")
        input("按任意键退出...")
        sys.exit(1)

if __name__ == "__main__":
    print("🚀 IndexTTS WebUI - 受保护版本")
    print("=" * 50)
    {var_names[3]}()
'''
    
    # 保存保护后的文件
    protected_filename = 'webui_protected.py'
    with open(protected_filename, 'w', encoding='utf-8') as f:
        f.write(protected_code)
    
    print(f"✅ 保护文件已生成: {protected_filename}")
    
    # 创建启动脚本
    create_startup_script()
    
    # 创建说明文件
    create_protection_readme()
    
    print("\n" + "=" * 60)
    print("🎉 代码保护完成！")
    print("=" * 60)
    print("🔐 受保护文件: webui_protected.py")
    print("🚀 启动脚本: 启动IndexTTS.bat")
    print("📖 使用说明: 保护版说明.txt")
    print("\n💡 保护特性:")
    print("   - 代码已编译为字节码")
    print("   - 字节码经过压缩和编码")
    print("   - 变量名随机化")
    print("   - 防止简单的代码查看")
    print("   - 修改后无法正常运行")
    
    return True

def create_startup_script():
    """创建启动脚本"""
    bat_content = '''@echo off
chcp 65001 >nul
title IndexTTS WebUI - 受保护版本

echo.
echo ========================================
echo 🚀 IndexTTS WebUI - 受保护版本
echo ========================================
echo.

if not exist "webui_protected.py" (
    echo ❌ 找不到程序文件: webui_protected.py
    echo 💡 请确保所有文件完整
    pause
    exit /b 1
)

echo 📂 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo 🚀 启动程序...
echo.

python webui_protected.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 💡 请检查错误信息
    pause
)
'''
    
    with open('启动IndexTTS.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print("✅ 启动脚本已创建: 启动IndexTTS.bat")

def create_protection_readme():
    """创建保护版说明"""
    readme_content = """# IndexTTS WebUI 受保护版本使用说明

## 文件说明
- `webui_protected.py`: 受保护的主程序文件
- `启动IndexTTS.bat`: 一键启动脚本
- `保护版说明.txt`: 本说明文件
- `预设样音/`: 预设音频文件夹

## 使用方法
1. 双击 `启动IndexTTS.bat` 启动程序
2. 或者在命令行运行: `python webui_protected.py`
3. 程序会自动打开浏览器访问WebUI界面

## 保护特性
- ✅ 代码已编译为字节码，无法直接查看源码
- ✅ 字节码经过压缩和Base64编码
- ✅ 变量名随机化，增加逆向难度
- ✅ 内置完整性检查，修改后无法运行
- ✅ 保留完整功能，用户体验不变

## 安全说明
- 此版本有效防止代码被轻易查看和修改
- 任何对程序文件的修改都会导致程序无法运行
- 提供基础的反盗版保护
- 适合一般用户的保护需求

## 注意事项
- 请勿修改 `webui_protected.py` 文件
- 确保Python环境正常安装
- 如遇问题请查看控制台错误信息
- 建议保留原始备份文件

## 分发说明
分发时请包含以下文件：
- webui_protected.py (必需)
- 启动IndexTTS.bat (推荐)
- 预设样音/ (如果需要)
- 保护版说明.txt (推荐)

## 故障排除
如果程序无法启动：
1. 检查Python是否正确安装
2. 确保所有依赖库已安装
3. 以管理员身份运行
4. 查看详细错误信息
"""
    
    with open('保护版说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文件已创建: 保护版说明.txt")

if __name__ == "__main__":
    if not os.path.exists('webui.py'):
        print("❌ 找不到webui.py文件")
        exit(1)
    
    success = obfuscate_code()
    
    if success:
        print("\n✅ 代码保护成功完成！")
        print("\n🎯 下一步:")
        print("   1. 测试运行: python webui_protected.py")
        print("   2. 或双击: 启动IndexTTS.bat")
        print("   3. 确认功能正常后可删除原始webui.py")
    else:
        print("\n❌ 代码保护失败！")
