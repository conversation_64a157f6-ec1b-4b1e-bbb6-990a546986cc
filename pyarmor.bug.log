[BUG]: out of license

## Command Line
C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\pyarmor gen --output encrypted_webui --recursive --exclude __pycache__ --exclude *.pyc --exclude dist --exclude build webui.py

## Environments
Python 3.10.0
Pyarmor 9.1.8 (trial), 000000, non-profits
Platform windows.x86_64
Native windows.amd64
Home C:\Users\<USER>\.pyarmor

## Traceback
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\__main__.py", line 793, in main
    main_entry(sys.argv[1:])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\__main__.py", line 786, in main_entry
    return args.func(ctx, args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\__main__.py", line 248, in cmd_gen
    builder.process(options)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\generate.py", line 188, in process
    self._obfuscate_scripts()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\generate.py", line 144, in _obfuscate_scripts
    code = Pytransform3.generate_obfuscated_script(self.ctx, r)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\core\__init__.py", line 95, in generate_obfuscated_script
    return m.generate_obfuscated_script(ctx, res)
  File "<maker>", line 715, in generate_obfuscated_script
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\pyarmor\cli\__init__.py", line 16, in process
    return meth(self, res, *args, **kwargs)
  File "<maker>", line 546, in process
  File "<maker>", line 552, in coserialize
  File "<maker>", line 597, in _build_ast_body
RuntimeError: out of license


