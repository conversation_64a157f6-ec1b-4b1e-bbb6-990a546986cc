#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS WebUI 加密打包脚本
功能：使用PyArmor混淆代码并用PyInstaller打包成exe
作者：AI Assistant
"""

import os
import sys
import shutil
import subprocess
import tempfile
from pathlib import Path

class WebUIEncryptor:
    """WebUI加密器"""
    
    def __init__(self):
        self.project_dir = Path.cwd()
        self.source_file = "webui.py"
        self.encrypted_dir = "encrypted_webui"
        self.dist_dir = "dist"
        self.build_dir = "build"
        
    def check_requirements(self):
        """检查必要的工具是否安装"""
        print("🔍 检查必要工具...")
        
        # 检查pyarmor
        try:
            result = subprocess.run(['pyarmor', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ PyArmor: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PyArmor未安装，请运行: pip install pyarmor")
            return False
            
        # 检查pyinstaller
        try:
            result = subprocess.run(['pyinstaller', '--version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✅ PyInstaller: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PyInstaller未安装，请运行: pip install pyinstaller")
            return False
            
        # 检查源文件
        if not os.path.exists(self.source_file):
            print(f"❌ 源文件不存在: {self.source_file}")
            return False
        print(f"✅ 源文件存在: {self.source_file}")
        
        return True
    
    def clean_previous_builds(self):
        """清理之前的构建文件"""
        print("🧹 清理之前的构建文件...")
        
        dirs_to_clean = [self.encrypted_dir, self.dist_dir, self.build_dir, "__pycache__"]
        files_to_clean = ["*.spec"]
        
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"   删除目录: {dir_name}")
                
        import glob
        for pattern in files_to_clean:
            for file_path in glob.glob(pattern):
                os.remove(file_path)
                print(f"   删除文件: {file_path}")
    
    def encrypt_with_pyarmor(self):
        """使用PyArmor加密代码"""
        print("🔐 使用PyArmor加密代码...")
        
        try:
            # 创建加密项目
            cmd = [
                'pyarmor', 'gen',
                '--output', self.encrypted_dir,
                '--recursive',  # 递归处理
                '--exclude', '__pycache__',  # 排除缓存目录
                '--exclude', '*.pyc',  # 排除编译文件
                '--exclude', 'dist',  # 排除分发目录
                '--exclude', 'build',  # 排除构建目录
                self.source_file
            ]
            
            print(f"   执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if result.returncode == 0:
                print("✅ PyArmor加密完成")
                return True
            else:
                print(f"❌ PyArmor加密失败: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ PyArmor加密失败: {e}")
            print(f"   错误输出: {e.stderr}")
            return False
    
    def create_pyinstaller_spec(self):
        """创建PyInstaller配置文件"""
        print("📝 创建PyInstaller配置文件...")
        
        encrypted_webui = os.path.join(self.encrypted_dir, "webui.py")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{encrypted_webui}'],
    pathex=[],
    binaries=[],
    datas=[
        ('预设样音', '预设样音'),  # 包含预设样音文件夹
        ('outputs', 'outputs'),   # 包含输出文件夹
    ],
    hiddenimports=[
        'gradio',
        'torch',
        'torchaudio', 
        'librosa',
        'soundfile',
        'numpy',
        'pandas',
        'demucs',
        'audio_separator',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='IndexTTS-WebUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台窗口以显示日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
        
        spec_file = "webui_encrypted.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
            
        print(f"✅ 配置文件已创建: {spec_file}")
        return spec_file

    def create_pyinstaller_spec_simple(self, source_file):
        """创建简化的PyInstaller配置文件"""
        print("📝 创建PyInstaller配置文件...")

        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{source_file}'],
    pathex=[],
    binaries=[],
    datas=[
        ('预设样音', '预设样音'),  # 包含预设样音文件夹
        ('outputs', 'outputs'),   # 包含输出文件夹（如果存在）
    ],
    hiddenimports=[
        'gradio',
        'torch',
        'torchaudio',
        'librosa',
        'soundfile',
        'numpy',
        'pandas',
        'demucs',
        'audio_separator',
        'tempfile',
        'threading',
        'logging',
        'traceback',
        'datetime',
        'shutil',
        'glob',
        'pathlib',
        'base64',
        'typing',
        'subprocess',
        'argparse',
        'json',
        'os',
        'sys',
        'time',
        're',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='IndexTTS-WebUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台窗口以显示日志
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''

        spec_file = "webui_simple.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)

        print(f"✅ 配置文件已创建: {spec_file}")
        return spec_file
    
    def build_with_pyinstaller(self, spec_file):
        """使用PyInstaller打包"""
        print("📦 使用PyInstaller打包...")
        
        try:
            cmd = [
                'pyinstaller',
                '--clean',  # 清理临时文件
                '--noconfirm',  # 不询问覆盖
                spec_file
            ]
            
            print(f"   执行命令: {' '.join(cmd)}")
            print("   这可能需要几分钟时间，请耐心等待...")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ PyInstaller打包完成")
                return True
            else:
                print(f"❌ PyInstaller打包失败:")
                print(f"   标准输出: {result.stdout}")
                print(f"   错误输出: {result.stderr}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ PyInstaller打包失败: {e}")
            return False
    
    def verify_output(self):
        """验证输出文件"""
        print("🔍 验证输出文件...")
        
        exe_path = os.path.join("dist", "IndexTTS-WebUI.exe")
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path)
            size_mb = file_size / (1024 * 1024)
            print(f"✅ 可执行文件已生成: {exe_path}")
            print(f"   文件大小: {size_mb:.1f} MB")
            return True
        else:
            print(f"❌ 可执行文件未找到: {exe_path}")
            return False
    
    def create_readme(self):
        """创建使用说明"""
        readme_content = """# IndexTTS WebUI 加密版使用说明

## 文件说明
- `IndexTTS-WebUI.exe`: 加密后的主程序
- `预设样音/`: 预设音频文件夹（如果存在）
- `outputs/`: 输出文件夹（程序运行时自动创建）

## 使用方法
1. 双击 `IndexTTS-WebUI.exe` 启动程序
2. 程序会自动打开浏览器访问WebUI界面
3. 如果浏览器没有自动打开，请手动访问控制台显示的地址

## 注意事项
- 首次运行可能需要较长时间加载
- 请确保系统已安装必要的运行库
- 如遇到问题，请查看控制台输出的错误信息

## 安全特性
- 代码已使用PyArmor进行混淆加密
- 无法直接查看或修改源代码
- 防止逆向工程和盗版
"""
        
        with open("dist/README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 使用说明已创建: dist/README.txt")

    def create_readme_simple(self, use_encrypted):
        """创建简化的使用说明"""
        if use_encrypted:
            security_info = """## 安全特性
- 代码已使用PyArmor进行混淆加密
- 无法直接查看或修改源代码
- 防止逆向工程和盗版"""
        else:
            security_info = """## 安全特性
- 代码已编译为字节码，难以直接修改
- PyInstaller打包提供基础保护
- 防止简单的代码查看和修改"""

        readme_content = f"""# IndexTTS WebUI 打包版使用说明

## 文件说明
- `IndexTTS-WebUI.exe`: 打包后的主程序
- `预设样音/`: 预设音频文件夹（如果存在）
- `outputs/`: 输出文件夹（程序运行时自动创建）

## 使用方法
1. 双击 `IndexTTS-WebUI.exe` 启动程序
2. 程序会自动打开浏览器访问WebUI界面
3. 如果浏览器没有自动打开，请手动访问控制台显示的地址

## 注意事项
- 首次运行可能需要较长时间加载
- 请确保系统已安装必要的运行库
- 如遇到问题，请查看控制台输出的错误信息

{security_info}
"""

        os.makedirs("dist", exist_ok=True)
        with open("dist/README.txt", 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("✅ 使用说明已创建: dist/README.txt")
    
    def run(self):
        """执行完整的加密打包流程"""
        print("=" * 60)
        print("🚀 IndexTTS WebUI 打包工具")
        print("=" * 60)

        # 检查PyInstaller
        try:
            result = subprocess.run(['pyinstaller', '--version'],
                                  capture_output=True, text=True, check=True)
            print(f"✅ PyInstaller: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ PyInstaller未安装，请运行: pip install pyinstaller")
            return False

        # 检查源文件
        if not os.path.exists(self.source_file):
            print(f"❌ 源文件不存在: {self.source_file}")
            return False
        print(f"✅ 源文件存在: {self.source_file}")

        # 清理之前的构建
        self.clean_previous_builds()

        # 尝试PyArmor加密（如果失败则跳过）
        print("🔐 尝试使用PyArmor加密...")
        if self.encrypt_with_pyarmor():
            print("✅ PyArmor加密成功")
            encrypted_webui = os.path.join(self.encrypted_dir, "webui.py")
            use_encrypted = True
        else:
            print("⚠️ PyArmor加密失败，使用原文件直接打包")
            print("💡 PyInstaller打包仍能有效防止代码修改")
            encrypted_webui = self.source_file
            use_encrypted = False

        # 创建PyInstaller配置
        spec_file = self.create_pyinstaller_spec_simple(encrypted_webui)

        # PyInstaller打包
        if not self.build_with_pyinstaller(spec_file):
            print("\n❌ 程序打包失败")
            return False

        # 验证输出
        if not self.verify_output():
            print("\n❌ 输出验证失败")
            return False

        # 创建说明文件
        self.create_readme_simple(use_encrypted)

        print("\n" + "=" * 60)
        if use_encrypted:
            print("🎉 加密打包完成！")
        else:
            print("🎉 打包完成！")
        print("=" * 60)
        print("📁 输出目录: dist/")
        print("📦 程序文件: dist/IndexTTS-WebUI.exe")
        print("📖 使用说明: dist/README.txt")
        print("\n💡 提示:")
        if use_encrypted:
            print("   - 代码已加密混淆，无法逆向")
        else:
            print("   - 代码已编译打包，难以修改")
        print("   - 可以安全分发给用户使用")
        print("   - 建议测试运行确保功能正常")

        return True

if __name__ == "__main__":
    encryptor = WebUIEncryptor()
    success = encryptor.run()
    
    if success:
        print("\n✅ 加密打包成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 加密打包失败！")
        sys.exit(1)
