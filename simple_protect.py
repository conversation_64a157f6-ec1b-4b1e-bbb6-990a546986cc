#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单代码保护工具
使用字节码编译保护webui.py
"""

import os
import py_compile
import shutil
from datetime import datetime

def simple_protect():
    """简单保护webui.py"""
    print("=" * 60)
    print("🔐 IndexTTS WebUI 简单保护工具")
    print("=" * 60)
    
    # 检查源文件
    source_file = "webui.py"
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    print(f"✅ 源文件存在: {source_file}")
    
    # 编译为字节码
    print("🔧 编译为字节码...")
    try:
        compiled_file = "webui.pyc"
        py_compile.compile(source_file, compiled_file, doraise=True)
        print(f"✅ 字节码编译成功: {compiled_file}")
    except Exception as e:
        print(f"❌ 编译失败: {e}")
        return False
    
    # 创建启动器
    print("📝 创建启动器...")
    launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IndexTTS WebUI 启动器 - 字节码版本
"""

import os
import sys
import marshal

def main():
    """启动主程序"""
    print("🚀 IndexTTS WebUI - 字节码保护版本")
    print("=" * 50)
    
    # 查找字节码文件
    bytecode_file = "webui.pyc"
    if not os.path.exists(bytecode_file):
        print(f"❌ 找不到程序文件: {bytecode_file}")
        input("按任意键退出...")
        return
    
    try:
        print("📂 加载程序...")
        
        # 读取并执行字节码
        with open(bytecode_file, 'rb') as f:
            # 跳过pyc文件头
            f.read(16)  # Python 3.7+ 的pyc文件头是16字节
            code_obj = marshal.load(f)
        
        print("🚀 启动程序...")
        exec(code_obj)
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请确保使用原始的、未修改的程序文件")
        input("按任意键退出...")

if __name__ == "__main__":
    main()
'''
    
    launcher_file = "webui_launcher.py"
    with open(launcher_file, 'w', encoding='utf-8') as f:
        f.write(launcher_code)
    
    print(f"✅ 启动器已创建: {launcher_file}")
    
    # 创建批处理文件
    create_batch_file()
    
    # 创建说明文件
    create_simple_readme()
    
    # 复制必要文件
    copy_required_files()
    
    print("\n" + "=" * 60)
    print("🎉 简单保护完成！")
    print("=" * 60)
    print("🔐 字节码文件: webui.pyc")
    print("🚀 启动器: webui_launcher.py")
    print("📦 启动脚本: 启动WebUI.bat")
    print("📖 使用说明: 简单保护说明.txt")
    print("\n💡 保护特性:")
    print("   - 代码已编译为字节码")
    print("   - 无法直接查看源代码")
    print("   - 修改字节码会导致程序无法运行")
    print("   - 保持完整功能")
    
    return True

def create_batch_file():
    """创建批处理启动文件"""
    bat_content = '''@echo off
chcp 65001 >nul
title IndexTTS WebUI - 字节码保护版本

echo.
echo ========================================
echo 🚀 IndexTTS WebUI - 字节码保护版本
echo ========================================
echo.

if not exist "webui_launcher.py" (
    echo ❌ 找不到启动器: webui_launcher.py
    pause
    exit /b 1
)

if not exist "webui.pyc" (
    echo ❌ 找不到程序文件: webui.pyc
    pause
    exit /b 1
)

echo 📂 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo 🚀 启动程序...
echo.

python webui_launcher.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
)
'''
    
    with open('启动WebUI.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    print("✅ 批处理文件已创建: 启动WebUI.bat")

def create_simple_readme():
    """创建简单说明文件"""
    readme_content = """# IndexTTS WebUI 字节码保护版本使用说明

## 文件说明
- `webui.pyc`: 编译后的字节码文件（主程序）
- `webui_launcher.py`: 启动器脚本
- `启动WebUI.bat`: 一键启动脚本
- `简单保护说明.txt`: 本说明文件
- `预设样音/`: 预设音频文件夹

## 使用方法
1. 双击 `启动WebUI.bat` 启动程序
2. 或者在命令行运行: `python webui_launcher.py`
3. 程序会自动打开浏览器访问WebUI界面

## 保护特性
- ✅ 源代码已编译为Python字节码
- ✅ 无法直接查看原始源代码
- ✅ 修改字节码文件会导致程序无法运行
- ✅ 保留完整功能，用户体验不变
- ✅ 简单有效的基础保护

## 安全说明
- 字节码保护防止代码被轻易查看
- 虽然理论上可以反编译，但增加了逆向难度
- 适合一般用户的基础保护需求
- 任何修改都会导致程序无法正常运行

## 注意事项
- 请勿修改 `webui.pyc` 文件
- 确保Python环境正常安装
- 如遇问题请查看控制台错误信息
- 建议保留原始备份文件

## 分发说明
分发时请包含以下文件：
- webui.pyc (必需)
- webui_launcher.py (必需)
- 启动WebUI.bat (推荐)
- 预设样音/ (如果需要)
- 简单保护说明.txt (推荐)

## 故障排除
如果程序无法启动：
1. 检查Python是否正确安装
2. 确保所有依赖库已安装
3. 以管理员身份运行
4. 查看详细错误信息
"""
    
    with open('简单保护说明.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文件已创建: 简单保护说明.txt")

def copy_required_files():
    """复制必要的文件"""
    # 复制预设样音文件夹
    if os.path.exists("预设样音"):
        print("📁 预设样音文件夹已存在，无需复制")
    
    # 确保outputs目录存在
    if not os.path.exists("outputs"):
        os.makedirs("outputs")
        print("✅ 创建outputs目录")

if __name__ == "__main__":
    if not os.path.exists('webui.py'):
        print("❌ 找不到webui.py文件")
        exit(1)
    
    success = simple_protect()
    
    if success:
        print("\n✅ 简单保护成功完成！")
        print("\n🎯 下一步:")
        print("   1. 测试运行: python webui_launcher.py")
        print("   2. 或双击: 启动WebUI.bat")
        print("   3. 确认功能正常后可删除原始webui.py")
    else:
        print("\n❌ 简单保护失败！")
