# type: ignore
import json
import os
import sys
import threading
import time
import pandas as pd
import logging
import traceback
from datetime import datetime
import shutil
import glob
from pathlib import Path  # type: ignore
import tempfile
import re
import base64
from typing import Dict, List, Optional, Tuple, Any  # type: ignore

# 显示中文启动标题（如果启用中文显示）
if os.environ.get('DISPLAY_CHINESE', '0') == '1':
    print()
    print("📣 Index-TTS (一款工业级可控且高效的零样本文本转语音系统)")
    print()
    print("================================================")
    print("🎁 程序打包：创客圈")
    print("🐧 交流QQ群：831541104")
    print("📱 技术微信：ZSCK2020")
    print("================================================")
    print()
    print("================================================")
    print("🔥 注意：整合包不可放在有中文名的文件夹中，否则会报错，请放在英文路径下!")
    print("🔥 程序启动过程中请耐心等待，不要关闭窗口，直至自动弹出webui界面!")
    print("================================================")
    print()

# 智能日志管理系统
class SmartLogger:
    """智能日志管理器 - 支持中文备注、解决方案和自动错误修复"""

    def __init__(self, log_file: str = "logs.txt"):
        self.log_file = log_file
        self.error_patterns = self._load_error_patterns()
        self.auto_fix_enabled = True
        self.use_chinese = os.environ.get('DISPLAY_CHINESE', '0') == '1'
        self.setup_logging()

    def setup_logging(self):
        """设置日志系统（跨平台兼容，解决权限和编码问题）"""
        import tempfile
        import sys
        import os

        # 创建自定义格式化器
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 尝试多个日志文件路径（更安全的路径策略）
        log_paths_to_try = [
            self.log_file,  # 首选：指定的日志文件
            os.path.join(tempfile.gettempdir(), f'indextts_{os.path.basename(self.log_file)}'),  # 备选：临时目录
            os.path.join(os.path.expanduser('~'), f'indextts_{os.path.basename(self.log_file)}'),  # 备选：用户目录
        ]

        file_handler = None
        log_file_created = False
        last_error = None

        # 尝试创建文件处理器
        for log_path in log_paths_to_try:
            try:
                # 确保目录存在
                log_dir = os.path.dirname(os.path.abspath(log_path))
                if log_dir and not os.path.exists(log_dir):
                    os.makedirs(log_dir, exist_ok=True)

                # 测试文件写入权限
                test_file = log_path + '.test'
                try:
                    with open(test_file, 'w', encoding='utf-8') as f:
                        f.write('test')
                    os.remove(test_file)
                except:
                    continue

                file_handler = logging.FileHandler(log_path, encoding='utf-8', mode='a')
                file_handler.setFormatter(formatter)
                self.log_file = log_path  # 更新实际使用的日志文件路径
                log_file_created = True
                break

            except (PermissionError, OSError) as e:
                last_error = e
                continue
            except Exception as e:
                last_error = e
                continue

        # 设置控制台处理器（安全的编码处理）
        try:
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setFormatter(formatter)
        except Exception:
            # 如果stderr有问题，使用stdout
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)

        # 配置根日志器
        handlers = [console_handler]
        if file_handler:
            handlers.append(file_handler)

        try:
            logging.basicConfig(
                level=logging.INFO,
                handlers=handlers,
                force=True
            )

            # 安全的状态消息输出
            if log_file_created:
                self._safe_print("SUCCESS: Smart logging system initialized")
                self._safe_print(f"Log file: {self.log_file}")
            else:
                self._safe_print("WARNING: Log file creation failed, console only")
                if last_error:
                    self._safe_print(f"Last error: {last_error}")

        except Exception as e:
            # 最安全的错误输出（避免所有编码问题）
            self._safe_print(f"ERROR: Log initialization failed: {e}")
            self._safe_print("Solutions:")
            self._safe_print("1. Check file permissions")
            self._safe_print("2. Ensure directory exists")
            self._safe_print("3. Run as administrator")

            # 最简单的日志配置
            try:
                logging.basicConfig(level=logging.INFO, handlers=[console_handler], force=True)
            except Exception:
                # 如果连基础配置都失败，使用最简单的配置
                logging.basicConfig(level=logging.INFO, force=True)

    def _safe_print(self, message):
        """安全的打印函数，避免Unicode编码错误"""
        try:
            print(message)
        except UnicodeEncodeError:
            # 移除所有非ASCII字符
            safe_message = ''.join(char for char in message if ord(char) < 128)
            print(safe_message)
        except Exception:
            # 最后的保险措施
            print("Log message encoding error")

    def _get_chinese_message(self, message):
        """将英文消息转换为中文消息"""
        if not self.use_chinese:
            return message

        # 中文消息映射
        chinese_map = {
            # 启动界面映射
            "IndexTTS - AI Voice Cloning System": "📣 Index-TTS (一款工业级可控且高效的零样本文本转语音系统)",
            "Intelligent Auto-Deployment": "",
            "Starting IndexTTS with automatic environment setup...": "",
            "This script will automatically handle all compatibility issues": "",
            "Including 50-series GPU support and Demucs installation": "",
            "Package by: ChuangKeQuan": "🎁 程序打包：创客圈",
            "QQ Group: 831541104": "🐧 交流QQ群：831541104",
            "WeChat: ZSCK2020": "📱 技术微信：ZSCK2020",
            "IMPORTANT: Do not place in Chinese path, use English path only!": "🔥 注意：整合包不可放在有中文名的文件夹中，否则会报错，请放在英文路径下!",
            "Please wait patiently, do not close window until webui opens!": "🔥 程序启动过程中请耐心等待，不要关闭窗口，直至自动弹出webui界面!",

            # 步骤映射
            "Step 1: Checking Python environment...": "🔍 步骤1: 检查Python环境...",
            "Python environment found": "✅ Python环境检查完成",
            "Step 2: Checking core files...": "🔍 步骤2: 检查核心文件...",
            "Core files verified": "✅ 核心文件验证完成",
            "Step 3: Setting up environment variables...": "🔧 步骤3: 配置环境变量...",
            "Environment variables configured": "✅ 环境变量配置完成",
            "Step 4: Launching IndexTTS...": "🚀 步骤4: 启动IndexTTS...",

            # WebUI启动映射
            "Starting IndexTTS WebUI": "🎵 启动IndexTTS网页界面",
            "The system will automatically:": "🤖 系统将自动执行以下操作:",
            "- Check and install Demucs for advanced voice separation": "    🎵 检查并安装Demucs先进人声分离技术",
            "- Detect and fix 50-series GPU compatibility issues": "    🎮 检测并修复50系显卡兼容性问题",
            "- Set up intelligent logging and monitoring": "    📊 设置智能日志记录和监控系统",
            "- Optimize performance for your hardware": "    ⚡ 根据您的硬件优化性能参数",
            "Please wait while the system initializes...": "⏳ 请稍候，系统正在初始化...",

            # 系统消息映射
            "SUCCESS: Smart logging system initialized": "✅ 智能日志系统初始化成功",
            "Log file:": "📄 日志文件:",
            "WARNING: Log file creation failed, console only": "⚠️ 日志文件创建失败，仅使用控制台输出",
            "Last error:": "❌ 最后错误:",
            "ERROR: Log initialization failed:": "❌ 日志初始化失败:",
            "Solutions:": "💡 解决方案:",
            "1. Check file permissions": "1. 检查文件权限",
            "2. Ensure directory exists": "2. 确保目录存在",
            "3. Run as administrator": "3. 以管理员身份运行",

            # 功能模块映射
            "Demucs - using advanced voice separation": "✅ 检测到Demucs - 使用最先进的人声分离技术",
            "Voice separation function loaded": "✅ 人声分离功能已加载",
            "Voice separation function enabled": "🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理",
            "Starting 50-series GPU compatibility check": "🎮 开始50系显卡兼容性检查...",
            "Detecting GPU model and CUDA compatibility": "🔍 正在检测GPU型号和CUDA兼容性...",
            "Detected GPU:": "🎮 检测到GPU:",
            "Non-50 series GPU, CUDA compatibility check skipped": "✅ 非50系显卡，CUDA兼容性检查跳过",
            "50-series GPU compatibility check completed": "✅ 50系显卡兼容性检查完成",
            "IndexTTS model initialization successful": "✅ IndexTTS模型初始化成功",
            "Found available port:": "🔍 找到可用端口:",
            "IndexTTS WebUI startup completed": "🚀 IndexTTS WebUI 启动完成",
            "Access URL:": "🌐 访问地址:"
        }

        # 查找匹配的中文翻译
        for eng, chn in chinese_map.items():
            if eng in message:
                return message.replace(eng, chn)

        return message

    def _load_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """加载错误模式和解决方案"""
        return {
            # CUDA相关错误
            "cuda_out_of_memory": {
                "pattern": r"CUDA out of memory|OutOfMemoryError",
                "description": "🔥 CUDA显存不足",
                "solutions": [
                    "减少batch_size参数",
                    "关闭其他占用GPU的程序",
                    "使用CPU模式运行",
                    "重启程序释放显存"
                ],
                "auto_fix": "reduce_batch_size"
            },

            # 文件权限错误
            "permission_denied": {
                "pattern": r"Permission denied|PermissionError",
                "description": "🔒 文件权限不足",
                "solutions": [
                    "以管理员身份运行程序",
                    "检查文件和目录权限",
                    "关闭占用文件的其他程序",
                    "更改文件所有者权限"
                ],
                "auto_fix": "fix_permissions"
            },

            # 模块导入错误
            "module_not_found": {
                "pattern": r"ModuleNotFoundError|ImportError.*No module named",
                "description": "📦 缺少必要的Python模块",
                "solutions": [
                    "运行pip install安装缺失模块",
                    "检查虚拟环境是否激活",
                    "更新pip到最新版本",
                    "使用国内镜像源安装"
                ],
                "auto_fix": "install_missing_module"
            },

            # 网络连接错误
            "network_error": {
                "pattern": r"ConnectionError|TimeoutError|URLError",
                "description": "🌐 网络连接问题",
                "solutions": [
                    "检查网络连接",
                    "使用代理或VPN",
                    "切换到国内镜像源",
                    "稍后重试"
                ],
                "auto_fix": "retry_with_mirror"
            },

            # 音频文件错误
            "audio_file_error": {
                "pattern": r"Error loading audio|Invalid audio file|UnsupportedFormat",
                "description": "🎵 音频文件格式或损坏问题",
                "solutions": [
                    "检查音频文件是否损坏",
                    "转换为支持的格式(WAV/MP3)",
                    "确保文件路径正确",
                    "使用其他音频文件测试"
                ],
                "auto_fix": "convert_audio_format"
            },

            # BigVGAN CUDA内核错误
            "bigvgan_cuda_error": {
                "pattern": r"Failed to load custom CUDA kernel for BigVGAN",
                "description": "⚡ BigVGAN CUDA内核加载失败",
                "solutions": [
                    "这是正常现象，程序会自动回退到PyTorch",
                    "如需优化性能，可安装Visual Studio Build Tools",
                    "或使用预编译版本",
                    "当前使用PyTorch后端，功能正常"
                ],
                "auto_fix": "ignore_bigvgan_warning"
            },

            # DeepSpeed错误
            "deepspeed_error": {
                "pattern": r"DeepSpeed加载失败|No module named 'deepspeed'",
                "description": "🚀 DeepSpeed优化库未安装",
                "solutions": [
                    "这是可选优化库，不影响核心功能",
                    "如需安装: pip install deepspeed",
                    "程序会自动使用标准推理模式",
                    "当前配置已足够使用"
                ],
                "auto_fix": "ignore_deepspeed_warning"
            }
        }

    def log_with_analysis(self, level: str, message: str, error: Exception = None):
        """带智能分析的日志记录（安全编码）"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # type: ignore
        _ = timestamp  # 保留变量以备后用

        # 基础日志记录（使用安全方法）
        self._safe_log(level, message)

        # 错误分析和解决方案
        if error or level.upper() in ["ERROR", "WARNING"]:
            self._analyze_and_suggest(message, error)

    def _analyze_and_suggest(self, message: str, error: Exception = None):
        """分析错误并提供解决方案"""
        error_text = str(error) if error else message
        _ = message  # 保留参数以备后用

        for error_type, config in self.error_patterns.items():
            if re.search(config["pattern"], error_text, re.IGNORECASE):
                self._log_error_analysis(error_type, config, error_text)

                # 尝试自动修复
                if self.auto_fix_enabled and "auto_fix" in config:
                    self._attempt_auto_fix(error_type, config["auto_fix"], error_text)
                break

    def _log_error_analysis(self, error_type: str, config: Dict, error_text: str):  # type: ignore
        """记录错误分析结果"""
        _ = error_type, error_text  # 保留参数以备后用
        logging.info(f"🔍 错误分析: {config['description']}")
        logging.info("💡 建议解决方案:")
        for i, solution in enumerate(config["solutions"], 1):
            logging.info(f"   {i}. {solution}")

    def _attempt_auto_fix(self, error_type: str, fix_method: str, error_text: str):  # type: ignore
        """尝试自动修复错误"""
        _ = error_type, error_text  # 保留参数以备后用
        try:
            if fix_method == "ignore_bigvgan_warning":
                logging.info("🔧 自动处理: BigVGAN警告已忽略，使用PyTorch后端")

            elif fix_method == "ignore_deepspeed_warning":
                logging.info("🔧 自动处理: DeepSpeed警告已忽略，使用标准推理")

            elif fix_method == "install_missing_module":
                module_match = re.search(r"No module named '([^']+)'", error_text)
                if module_match:
                    module_name = module_match.group(1)
                    logging.info(f"🔧 尝试自动安装模块: {module_name}")
                    # 这里可以添加自动安装逻辑

            elif fix_method == "reduce_batch_size":
                logging.info("🔧 建议: 请在设置中减少batch_size参数")

            elif fix_method == "fix_permissions":
                logging.info("🔧 建议: 请以管理员身份重新运行程序")

        except Exception as fix_error:
            logging.error(f"❌ 自动修复失败: {fix_error}")

    def _safe_log(self, level: str, message: str):
        """安全的日志输出（避免编码错误）"""
        try:
            if level.upper() == "INFO":
                logging.info(message)
            elif level.upper() == "WARNING":
                logging.warning(message)
            elif level.upper() == "ERROR":
                logging.error(message)
            else:
                logging.info(message)
        except UnicodeEncodeError:
            # 移除emoji和特殊字符，使用ASCII替代
            safe_message = (message
                           .replace("✅", "[SUCCESS]")
                           .replace("⚠️", "[WARNING]")
                           .replace("❌", "[ERROR]")
                           .replace("🔍", "[INFO]")
                           .replace("💡", "[TIP]")
                           .replace("📝", "[NOTE]")
                           .replace("🔧", "[FIX]")
                           .replace("🗑️", "[DELETE]")
                           .replace("📊", "[STATS]")
                           .replace("📄", "[FILE]")
                           .replace("🎵", "[AUDIO]")
                           .replace("🚀", "[START]")
                           .replace("🛑", "[STOP]")
                           .replace("👋", "[BYE]")
                           .replace("🔄", "[PROCESS]"))
            try:
                if level.upper() == "INFO":
                    logging.info(safe_message)
                elif level.upper() == "WARNING":
                    logging.warning(safe_message)
                elif level.upper() == "ERROR":
                    logging.error(safe_message)
                else:
                    logging.info(safe_message)
            except UnicodeEncodeError:
                # 最后的保险措施：只保留ASCII字符
                ascii_message = ''.join(char for char in safe_message if ord(char) < 128)
                logging.info(f"[{level.upper()}] {ascii_message}")

    def log_info(self, message: str):
        """记录信息日志"""
        chinese_message = self._get_chinese_message(message)
        self._safe_log("INFO", chinese_message)

    def log_warning(self, message: str, error: Exception = None):
        """记录警告日志"""
        chinese_message = self._get_chinese_message(message)
        self.log_with_analysis("WARNING", chinese_message, error)

    def log_error(self, message: str, error: Exception = None):
        """记录错误日志"""
        chinese_message = self._get_chinese_message(message)
        self.log_with_analysis("ERROR", chinese_message, error)

    def log_success(self, message: str):
        """记录成功日志"""
        chinese_message = self._get_chinese_message(message)
        self._safe_log("INFO", f"✅ {chinese_message}")

    def log_process_start(self, process_name: str):
        """记录流程开始"""
        logging.info(f"🔄 开始: {process_name}")

    def log_process_end(self, process_name: str, success: bool = True):
        """记录流程结束"""
        if success:
            logging.info(f"✅ 完成: {process_name}")
        else:
            logging.error(f"❌ 失败: {process_name}")

# 实时日志分析器类
class LogAnalyzer:
    """实时日志分析器 - 集成在webui.py中"""

    def __init__(self, log_file: str = "logs.txt"):
        self.log_file = log_file
        self.last_position = 0
        self.error_patterns = self._load_error_patterns()
        self.auto_fix_enabled = True
        self.analysis_results = []
        self.monitoring = False

    def _load_error_patterns(self) -> Dict:
        """加载错误模式和解决方案"""
        return {
            # CUDA内存错误
            "cuda_oom": {
                "pattern": r"CUDA out of memory|OutOfMemoryError|RuntimeError.*memory",
                "severity": "高",
                "description": "🔥 CUDA显存不足错误",
                "chinese_explanation": "显卡内存不够用，无法处理当前任务",
                "solutions": [
                    "1. 减少batch_size参数（推荐设置为1-4）",
                    "2. 关闭其他占用GPU的程序（如游戏、挖矿软件）",
                    "3. 重启程序释放显存缓存",
                    "4. 使用CPU模式运行（速度较慢但稳定）"
                ],
                "auto_fix": "reduce_batch_size"
            },

            # 模块导入错误
            "module_not_found": {
                "pattern": r"ModuleNotFoundError|ImportError.*No module named",
                "severity": "中",
                "description": "📦 Python模块缺失错误",
                "chinese_explanation": "程序需要的Python库没有安装",
                "solutions": [
                    "1. 使用pip install安装缺失的模块",
                    "2. 检查是否在正确的虚拟环境中",
                    "3. 更新pip到最新版本：python -m pip install --upgrade pip",
                    "4. 使用国内镜像源：pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/"
                ],
                "auto_fix": "install_missing_module"
            },

            # 文件权限错误
            "permission_error": {
                "pattern": r"PermissionError|Permission denied",
                "severity": "中",
                "description": "🔒 文件权限不足错误",
                "chinese_explanation": "程序没有足够的权限访问文件或目录",
                "solutions": [
                    "1. 以管理员身份运行程序",
                    "2. 关闭占用文件的其他程序（如文本编辑器）",
                    "3. 检查文件属性，取消只读设置",
                    "4. 修改文件夹权限，给予完全控制权限"
                ],
                "auto_fix": "fix_permissions"
            },

            # BigVGAN CUDA内核错误
            "bigvgan_error": {
                "pattern": r"Failed to load custom CUDA kernel for BigVGAN",
                "severity": "低",
                "description": "⚡ BigVGAN CUDA内核加载失败",
                "chinese_explanation": "BigVGAN的CUDA加速内核无法加载，但不影响功能",
                "solutions": [
                    "1. 这是正常现象，程序会自动使用PyTorch后端",
                    "2. 如需优化性能，可安装Visual Studio Build Tools",
                    "3. 当前使用PyTorch后端，功能完全正常"
                ],
                "auto_fix": "ignore_warning"
            },

            # DeepSpeed错误
            "deepspeed_error": {
                "pattern": r"DeepSpeed.*失败|No module named 'deepspeed'",
                "severity": "低",
                "description": "🚀 DeepSpeed优化库未安装",
                "chinese_explanation": "DeepSpeed是可选的性能优化库，不影响核心功能",
                "solutions": [
                    "1. 这是可选优化库，不影响核心功能",
                    "2. 如需安装：pip install deepspeed",
                    "3. 程序会自动使用标准推理模式"
                ],
                "auto_fix": "ignore_warning"
            }
        }

    def start_monitoring(self):
        """启动后台日志监控"""
        if self.monitoring:
            return

        self.monitoring = True
        import threading

        def monitor_loop():
            while self.monitoring:
                try:
                    if os.path.exists(self.log_file):
                        new_lines = self._read_new_lines()
                        if new_lines:
                            self._analyze_lines(new_lines)

                    time.sleep(2)  # 每2秒检查一次

                except Exception as e:
                    smart_logger.log_error(f"日志监控错误: {e}")
                    time.sleep(5)

        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        smart_logger.log_info("🔍 智能日志监控已启动")

    def stop_monitoring(self):
        """停止日志监控"""
        self.monitoring = False
        smart_logger.log_info("🛑 智能日志监控已停止")

    def _read_new_lines(self) -> List[str]:
        """读取新增的日志行"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                f.seek(self.last_position)
                new_lines = f.readlines()
                self.last_position = f.tell()
                return [line.strip() for line in new_lines if line.strip()]
        except Exception:
            return []

    def _analyze_lines(self, lines: List[str]):
        """分析日志行"""
        for line in lines:
            # 检查是否包含错误
            if any(keyword in line.lower() for keyword in ['error', 'failed', 'exception']):
                self._analyze_error(line)

    def _analyze_error(self, error_line: str):
        """分析错误行"""
        # 匹配错误模式
        for error_type, config in self.error_patterns.items():
            if re.search(config["pattern"], error_line, re.IGNORECASE):
                self._log_error_analysis(error_type, config, error_line)

                # 尝试自动修复
                if self.auto_fix_enabled and "auto_fix" in config:
                    self._attempt_auto_fix(error_type, config["auto_fix"])
                break

    def _log_error_analysis(self, error_type: str, config: Dict, error_line: str):
        """记录错误分析结果"""
        smart_logger.log_info("=" * 60)
        smart_logger.log_info("📋 智能错误分析报告")
        smart_logger.log_info("=" * 60)
        smart_logger.log_info(f"🏷️  错误类型: {config['description']}")
        smart_logger.log_info(f"⚠️  严重程度: {config['severity']}")
        smart_logger.log_info(f"📝 中文说明: {config['chinese_explanation']}")

        smart_logger.log_info("💡 解决方案:")
        for solution in config['solutions']:
            smart_logger.log_info(f"   {solution}")

        smart_logger.log_info("=" * 60)

    def _attempt_auto_fix(self, error_type: str, auto_fix: str):
        """尝试自动修复"""
        try:
            if auto_fix == "ignore_warning":
                smart_logger.log_info("🔧 自动处理: 已标记为可忽略警告")
            elif auto_fix == "reduce_batch_size":
                smart_logger.log_info("🔧 建议: 请在设置中减少batch_size参数")
            elif auto_fix == "fix_permissions":
                smart_logger.log_info("🔧 建议: 请以管理员身份重新运行程序")
            elif auto_fix == "install_missing_module":
                smart_logger.log_info("🔧 建议: 请使用pip install安装缺失的模块")

        except Exception as e:
            smart_logger.log_error(f"自动修复失败: {e}")

# 创建全局日志实例
smart_logger = SmartLogger()
log_analyzer = LogAnalyzer()

# 检查并自动安装人声分离依赖
VOCAL_SEPARATION_AVAILABLE = False

def auto_install_vocal_separation():
    """自动安装Demucs人声分离依赖"""
    import subprocess
    import sys

    print("=" * 60)
    print("🎵 IndexTTS Demucs人声分离功能自动安装")
    print("=" * 60)
    print("🔄 检测到人声分离依赖缺失，开始自动安装...")
    print("💡 这是首次启动的一次性安装，请耐心等待...")
    print("🚀 使用Facebook Demucs - 目前最先进的开源人声分离技术")

    # 国内镜像源列表
    mirrors = [
        ("清华大学", "https://pypi.tuna.tsinghua.edu.cn/simple/"),
        ("豆瓣", "https://pypi.douban.com/simple/"),
        ("阿里云", "https://mirrors.aliyun.com/pypi/simple/"),
        ("中科大", "https://pypi.mirrors.ustc.edu.cn/simple/"),
    ]

    # 使用Demucs作为主要人声分离方案
    packages = [
        ("demucs", "Facebook Demucs人声分离"),
        ("librosa>=0.10.0", "音频分析库"),
        ("soundfile>=0.12.0", "音频文件处理"),
        ("torch>=1.13.0", "PyTorch深度学习框架"),
        ("torchaudio>=0.13.0", "PyTorch音频处理")
    ]

    smart_logger.log_info(f"📦 需要安装 {len(packages)} 个依赖包:")
    for pkg, desc in packages:
        smart_logger.log_info(f"   • {pkg} - {desc}")

    success_count = 0
    for pkg, desc in packages:
        smart_logger.log_process_start(f"安装 {pkg} ({desc})")
        installed = False

        for mirror_name, mirror_url in mirrors:
            try:
                smart_logger.log_info(f"   📡 尝试使用 {mirror_name} 镜像源...")
                cmd = [sys.executable, "-m", "pip", "install", pkg, "-i", mirror_url, "--quiet", "--no-warn-script-location"]
                result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=180)
                smart_logger.log_success(f"{pkg} 安装成功")
                installed = True
                success_count += 1
                break
            except subprocess.TimeoutExpired as e:
                smart_logger.log_warning(f"{mirror_name} 镜像源超时，尝试下一个...", e)
                continue
            except subprocess.CalledProcessError as e:
                smart_logger.log_warning(f"{mirror_name} 镜像源失败，尝试下一个...", e)
                continue

        if not installed:
            smart_logger.log_error(f"{pkg} 所有镜像源都安装失败")

    smart_logger.log_info(f"📊 安装结果: {success_count}/{len(packages)} 个包安装成功")

    if success_count >= 4:  # 至少需要demucs, librosa, soundfile, torch
        smart_logger.log_success("Demucs人声分离依赖安装完成！")
        smart_logger.log_info("✨ 现在可以享受最先进的AI人声分离功能了")
        return True
    elif success_count > 0:
        smart_logger.log_warning(f"部分依赖安装成功，可能影响人声分离功能")
        return False
    else:
        smart_logger.log_error("所有依赖安装失败，将使用基础功能")
        return False

# 人声分离相关的类型定义和全局变量
VOCAL_SEPARATION_AVAILABLE = False
VOCAL_SEPARATION_METHOD = "none"

# 尝试导入人声分离依赖，优先使用Demucs
try:
    import librosa  # type: ignore
    import soundfile as sf  # type: ignore
    import numpy as np  # type: ignore

    # 优先尝试导入Demucs（最先进的人声分离）
    try:
        import demucs  # type: ignore
        from demucs import pretrained  # type: ignore
        import torch  # type: ignore
        globals()['demucs'] = demucs
        globals()['demucs_pretrained'] = pretrained
        globals()['torch'] = torch
        VOCAL_SEPARATION_METHOD = "demucs"
        smart_logger.log_success("✅ 检测到Demucs - 使用最先进的人声分离技术")
    except ImportError:
        # 如果Demucs不可用，强制安装
        smart_logger.log_warning("⚠️ 未检测到Demucs，开始自动安装...")
        if auto_install_vocal_separation():
            try:
                import demucs  # type: ignore
                from demucs import pretrained  # type: ignore
                import torch  # type: ignore
                globals()['demucs'] = demucs
                globals()['demucs_pretrained'] = pretrained
                globals()['torch'] = torch
                VOCAL_SEPARATION_METHOD = "demucs"
                smart_logger.log_success("✅ Demucs自动安装成功 - 使用最先进的人声分离技术")
            except ImportError:
                # 如果Demucs安装失败，尝试audio-separator
                try:
                    import importlib
                    audio_separator_module = importlib.import_module('audio_separator.separator')
                    globals()['AudioSeparator'] = audio_separator_module.Separator
                    VOCAL_SEPARATION_METHOD = "audio_separator"
                    smart_logger.log_info("🎵 使用audio-separator - 高质量人声分离")
                except ImportError:
                    VOCAL_SEPARATION_METHOD = "librosa_only"
                    smart_logger.log_warning("⚠️ 仅使用librosa - 基础人声分离")
        else:
            # 安装失败，尝试其他方案
            try:
                import importlib
                audio_separator_module = importlib.import_module('audio_separator.separator')
                globals()['AudioSeparator'] = audio_separator_module.Separator
                VOCAL_SEPARATION_METHOD = "audio_separator"
                smart_logger.log_info("🎵 检测到audio-separator - 使用高质量人声分离")
            except ImportError:
                VOCAL_SEPARATION_METHOD = "librosa_only"
                smart_logger.log_warning("⚠️ 仅检测到librosa - 使用基础人声分离")

    VOCAL_SEPARATION_AVAILABLE = True
    smart_logger.log_success(f"人声分离功能已加载 (方法: {VOCAL_SEPARATION_METHOD})")
except ImportError as e:
    smart_logger.log_warning(f"人声分离依赖缺失: {e}")

    # 自动安装依赖
    if auto_install_vocal_separation():
        try:
            # 重新尝试导入
            import librosa  # type: ignore
            import soundfile as sf  # type: ignore
            import numpy as np  # type: ignore

            # 优先尝试Demucs
            try:
                import demucs.api  # type: ignore
                import torch  # type: ignore
                globals()['DemucsAPI'] = demucs.api
                globals()['torch'] = torch
                VOCAL_SEPARATION_METHOD = "demucs"
                smart_logger.log_success("Demucs安装成功 - 使用最先进的人声分离技术")
            except ImportError:
                try:
                    import importlib
                    audio_separator_module = importlib.import_module('audio_separator.separator')
                    globals()['AudioSeparator'] = audio_separator_module.Separator
                    VOCAL_SEPARATION_METHOD = "audio_separator"
                    smart_logger.log_info("🎵 audio-separator安装成功 - 使用高质量人声分离")
                except ImportError:
                    VOCAL_SEPARATION_METHOD = "librosa_only"
                    smart_logger.log_warning("仅librosa可用 - 使用基础人声分离")

            VOCAL_SEPARATION_AVAILABLE = True
            smart_logger.log_success(f"人声分离功能安装并加载成功 (方法: {VOCAL_SEPARATION_METHOD})")
        except ImportError as e2:
            smart_logger.log_error(f"安装后仍无法导入: {e2}")
            VOCAL_SEPARATION_AVAILABLE = False
            VOCAL_SEPARATION_METHOD = "none"
    else:
        smart_logger.log_error("人声分离依赖安装失败，将使用基础功能")
        VOCAL_SEPARATION_AVAILABLE = False
        VOCAL_SEPARATION_METHOD = "none"

# 显示人声分离功能状态
if VOCAL_SEPARATION_AVAILABLE:
    smart_logger.log_info("🎵 人声分离功能已启用 - 上传的样音将自动进行人声分离处理")
else:
    smart_logger.log_warning("人声分离功能未启用 - 将使用原始音频进行语音合成")

# 50系显卡CUDA兼容性检测和修复
def check_and_fix_rtx50_cuda():
    """检测并修复50系显卡的CUDA兼容性问题"""
    try:
        import torch
        import warnings
        import os

        # 检测是否为50系显卡
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0).lower()
            smart_logger.log_info(f"🎮 检测到GPU: {torch.cuda.get_device_name(0)}")

            # 检测是否为50系显卡 - 扩展检测范围
            is_rtx50_series = any(x in gpu_name for x in [
                'rtx 50', 'rtx50', '5070', '5080', '5090',
                'geforce rtx 5070', 'geforce rtx 5080', 'geforce rtx 5090'
            ])

            if is_rtx50_series:
                smart_logger.log_warning("⚠️ 检测到NVIDIA GeForce RTX 50系显卡")
                smart_logger.log_info("🔧 开始CUDA内核兼容性检测...")

                # 检测CUDA版本兼容性
                cuda_version = torch.version.cuda
                torch_version = torch.__version__
                smart_logger.log_info(f"📊 当前环境: CUDA {cuda_version}, PyTorch {torch_version}")

                # 设置50系显卡优化环境变量
                os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
                os.environ['TORCH_USE_CUDA_DSA'] = '1'
                os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

                # 执行CUDA兼容性测试
                compatibility_issues = []

                try:
                    # 捕获CUDA兼容性警告
                    with warnings.catch_warnings(record=True) as w:
                        warnings.simplefilter("always")

                        # 测试基础CUDA操作
                        smart_logger.log_info("🧪 执行CUDA基础功能测试...")
                        test_tensor = torch.randn(100, 100).cuda()
                        result = test_tensor @ test_tensor.T
                        del test_tensor, result
                        torch.cuda.empty_cache()

                        # 检查CUDA兼容性警告
                        cuda_warnings = [warning for warning in w if "CUDA capability" in str(warning.message) or "sm_" in str(warning.message)]

                        if cuda_warnings:
                            for warning in cuda_warnings:
                                warning_msg = str(warning.message)
                                compatibility_issues.append(f"CUDA兼容性警告: {warning_msg}")
                                smart_logger.log_error(f"❌ CUDA兼容性问题: {warning_msg}")

                                # 详细分析CUDA兼容性问题
                                if "sm_120" in warning_msg:
                                    smart_logger.log_error("📋 问题分析: 50系显卡使用CUDA架构sm_120，当前PyTorch不支持")
                                    smart_logger.log_info("💡 解决方案: 需要安装支持sm_120架构的PyTorch版本")
                                elif "CUDA capability" in warning_msg:
                                    smart_logger.log_error("📋 问题分析: GPU的CUDA计算能力与PyTorch版本不匹配")
                                    smart_logger.log_info("💡 解决方案: 需要更新PyTorch到兼容版本")

                        # 测试Demucs相关的CUDA操作
                        smart_logger.log_info("🧪 执行Demucs CUDA兼容性测试...")
                        try:
                            # 模拟Demucs的CUDA操作
                            test_tensor = torch.randn(1, 2, 44100).cuda()  # 模拟音频数据
                            conv_test = torch.nn.Conv1d(2, 64, 7, padding=3).cuda()
                            output = conv_test(test_tensor)
                            del test_tensor, conv_test, output
                            torch.cuda.empty_cache()
                            smart_logger.log_success("✅ Demucs CUDA兼容性测试通过")
                        except Exception as demucs_e:
                            error_msg = str(demucs_e)
                            compatibility_issues.append(f"Demucs CUDA错误: {error_msg}")
                            smart_logger.log_error(f"❌ Demucs CUDA测试失败: {error_msg}")

                            # 详细分析Demucs错误
                            if "no kernel image is available" in error_msg:
                                smart_logger.log_error("📋 问题分析: CUDA内核映像不可用，GPU架构不被支持")
                                smart_logger.log_info("💡 解决方案: 需要安装支持当前GPU架构的PyTorch版本")
                            elif "out of memory" in error_msg.lower():
                                smart_logger.log_error("📋 问题分析: GPU内存不足")
                                smart_logger.log_info("💡 解决方案: 降低批处理大小或使用CPU模式")
                            elif "device-side assert" in error_msg:
                                smart_logger.log_error("📋 问题分析: GPU设备端断言失败")
                                smart_logger.log_info("💡 解决方案: 可能是驱动问题，需要更新NVIDIA驱动")

                        if compatibility_issues:
                            smart_logger.log_error("❌ 检测到50系显卡CUDA兼容性问题")
                            smart_logger.log_error("📋 问题详情:")
                            for issue in compatibility_issues:
                                smart_logger.log_error(f"   • {issue}")

                            # 提供详细的修复计划
                            smart_logger.log_info("🔧 自动修复计划:")
                            smart_logger.log_info("   1. 尝试安装最新nightly版本PyTorch")
                            smart_logger.log_info("   2. 尝试不同CUDA版本的PyTorch")
                            smart_logger.log_info("   3. 如果都失败，降级到CPU模式")
                            smart_logger.log_info("🚀 开始执行自动修复...")

                            # 尝试修复方案
                            return fix_rtx50_cuda_compatibility(compatibility_issues)
                        else:
                            smart_logger.log_success("✅ 50系显卡CUDA兼容性测试通过")
                            smart_logger.log_success("✅ Demucs和GPU模式可正常使用")
                            return True

                except RuntimeError as e:
                    error_msg = str(e)
                    compatibility_issues.append(f"CUDA运行时错误: {error_msg}")

                    # 详细分析RuntimeError
                    if "no kernel image is available" in error_msg:
                        smart_logger.log_error("❌ 检测到CUDA内核映像不可用错误")
                        smart_logger.log_error(f"📋 错误详情: {error_msg}")
                        smart_logger.log_error("🔍 问题分析:")
                        smart_logger.log_error("   • 当前PyTorch版本不支持50系显卡的CUDA架构(sm_120)")
                        smart_logger.log_error("   • GPU驱动版本可能过旧")
                        smart_logger.log_error("   • CUDA工具包版本不匹配")
                        smart_logger.log_info("💡 自动修复方案:")
                        smart_logger.log_info("   1. 安装最新nightly版本PyTorch")
                        smart_logger.log_info("   2. 尝试不同CUDA版本组合")
                        smart_logger.log_info("   3. 更新NVIDIA驱动")

                    elif "CUDA capability" in error_msg:
                        smart_logger.log_error("❌ 检测到CUDA计算能力不匹配错误")
                        smart_logger.log_error(f"📋 错误详情: {error_msg}")
                        smart_logger.log_error("🔍 问题分析:")
                        smart_logger.log_error("   • GPU的CUDA计算能力版本不被当前PyTorch支持")
                        smart_logger.log_error("   • 需要更新PyTorch到支持新架构的版本")
                        smart_logger.log_info("💡 自动修复方案:")
                        smart_logger.log_info("   1. 安装支持sm_120架构的PyTorch版本")
                        smart_logger.log_info("   2. 尝试预发布版本PyTorch")

                    elif "device-side assert" in error_msg:
                        smart_logger.log_error("❌ 检测到GPU设备端断言失败")
                        smart_logger.log_error(f"📋 错误详情: {error_msg}")
                        smart_logger.log_error("🔍 问题分析:")
                        smart_logger.log_error("   • GPU驱动可能存在问题")
                        smart_logger.log_error("   • CUDA内核执行异常")
                        smart_logger.log_info("💡 自动修复方案:")
                        smart_logger.log_info("   1. 设置CUDA_LAUNCH_BLOCKING=1进行调试")
                        smart_logger.log_info("   2. 尝试不同版本的PyTorch")
                        smart_logger.log_info("   3. 建议更新NVIDIA驱动")

                    else:
                        smart_logger.log_error("❌ 检测到未知CUDA运行时错误")
                        smart_logger.log_error(f"📋 错误详情: {error_msg}")
                        smart_logger.log_error("🔍 问题分析:")
                        smart_logger.log_error("   • 可能是新的兼容性问题")
                        smart_logger.log_error("   • 需要进一步诊断")
                        smart_logger.log_info("💡 自动修复方案:")
                        smart_logger.log_info("   1. 尝试多个PyTorch版本")
                        smart_logger.log_info("   2. 如果都失败，使用CPU模式")

                    smart_logger.log_info("🚀 开始执行自动修复...")
                    # 尝试修复方案
                    return fix_rtx50_cuda_compatibility(compatibility_issues)
            else:
                smart_logger.log_info("✅ 非50系显卡，CUDA兼容性检查跳过")
                return True
        else:
            smart_logger.log_info("💡 未检测到CUDA设备，将使用CPU模式")
            return True

    except Exception as e:
        smart_logger.log_error(f"❌ CUDA兼容性检查失败: {e}")
        return False

def fix_rtx50_cuda_compatibility(compatibility_issues=None):
    """修复50系显卡的CUDA兼容性问题"""
    import subprocess
    import sys
    import os
    import time

    smart_logger.log_info("=" * 80)
    smart_logger.log_info("🔧 NVIDIA GeForce RTX 50系显卡CUDA兼容性自动修复")
    smart_logger.log_info("=" * 80)

    if compatibility_issues:
        smart_logger.log_error("📋 检测到的兼容性问题:")
        for i, issue in enumerate(compatibility_issues, 1):
            smart_logger.log_error(f"   {i}. {issue}")
        smart_logger.log_info("")

    smart_logger.log_info("🚀 开始自动修复流程...")
    smart_logger.log_info("⏳ 预计修复时间: 2-5分钟")
    smart_logger.log_info("💡 请耐心等待，不要关闭程序")
    smart_logger.log_info("")

    # 记录修复开始时间
    start_time = time.time()

    # 修复方案1: 重新安装兼容的PyTorch版本
    smart_logger.log_info("🚀 修复方案1: 安装50系显卡兼容的PyTorch版本")
    smart_logger.log_info("💡 原理: 50系显卡使用新的CUDA架构(sm_120)，需要最新版本的PyTorch支持")

    try:
        # 备份当前环境信息
        try:
            import torch
            current_version = torch.__version__
            current_cuda = torch.version.cuda
            smart_logger.log_info(f"📊 当前版本: PyTorch {current_version}, CUDA {current_cuda}")
        except:
            smart_logger.log_info("📊 当前PyTorch环境异常，需要重新安装")

        # 卸载现有的torch
        smart_logger.log_info("🗑️  步骤1: 卸载现有PyTorch...")
        smart_logger.log_info("⏳ 正在卸载，请稍候...")

        try:
            uninstall_result = subprocess.run([sys.executable, "-m", "pip", "uninstall", "torch", "torchaudio", "-y"],
                          capture_output=True, text=True, timeout=120)

            if uninstall_result.returncode == 0:
                smart_logger.log_success("✅ PyTorch卸载成功")
            else:
                smart_logger.log_warning(f"⚠️ PyTorch卸载警告: {uninstall_result.stderr}")
        except subprocess.TimeoutExpired:
            smart_logger.log_warning("⚠️ PyTorch卸载超时，继续执行安装...")
        except Exception as e:
            smart_logger.log_warning(f"⚠️ PyTorch卸载异常: {e}")

        smart_logger.log_info("✅ 卸载步骤完成，开始安装新版本...")

        # 针对50系显卡的PyTorch安装策略
        smart_logger.log_info("📦 步骤2: 安装兼容版本的PyTorch...")
        smart_logger.log_info("🎯 安装策略: 优先尝试最新nightly版本，然后是稳定版本")

        install_commands = [
            # 最新nightly版本（最可能支持50系显卡）
            {
                "cmd": [sys.executable, "-m", "pip", "install", "--pre", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/nightly/cu124"],
                "desc": "PyTorch Nightly (CUDA 12.4) - 最新开发版本",
                "reason": "包含最新的50系显卡支持"
            },
            {
                "cmd": [sys.executable, "-m", "pip", "install", "--pre", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/nightly/cu121"],
                "desc": "PyTorch Nightly (CUDA 12.1) - 开发版本",
                "reason": "兼容性更好的CUDA版本"
            },
            # 最新稳定版
            {
                "cmd": [sys.executable, "-m", "pip", "install", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu124"],
                "desc": "PyTorch Stable (CUDA 12.4) - 最新稳定版",
                "reason": "稳定版本，可能支持50系显卡"
            },
            {
                "cmd": [sys.executable, "-m", "pip", "install", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu121"],
                "desc": "PyTorch Stable (CUDA 12.1) - 稳定版",
                "reason": "广泛兼容的CUDA版本"
            },
            # CPU版本作为最后备选
            {
                "cmd": [sys.executable, "-m", "pip", "install", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cpu"],
                "desc": "PyTorch CPU版本 - 最后备选",
                "reason": "确保程序能够运行，但性能较慢"
            }
        ]

        for i, install_config in enumerate(install_commands):
            cmd = install_config["cmd"]
            desc = install_config["desc"]
            reason = install_config["reason"]

            try:
                smart_logger.log_info(f"🔄 方案 {i+1}: {desc}")
                smart_logger.log_info(f"💡 选择原因: {reason}")
                smart_logger.log_info("⏳ 正在下载和安装，这可能需要几分钟时间...")
                smart_logger.log_info("📡 连接PyTorch官方服务器...")

                install_start = time.time()

                # 使用实时输出的方式执行安装
                import threading
                import queue

                def run_install():
                    try:
                        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                                 text=True, bufsize=1, universal_newlines=True)

                        # 实时读取输出
                        while True:
                            output = process.stdout.readline()
                            if output == '' and process.poll() is not None:
                                break
                            if output:
                                # 过滤和显示关键信息
                                output = output.strip()
                                if any(keyword in output.lower() for keyword in ['downloading', 'installing', 'collecting', 'building', 'successfully']):
                                    if 'downloading' in output.lower():
                                        smart_logger.log_info(f"📥 {output}")
                                    elif 'installing' in output.lower():
                                        smart_logger.log_info(f"🔧 {output}")
                                    elif 'successfully' in output.lower():
                                        smart_logger.log_success(f"✅ {output}")
                                    else:
                                        smart_logger.log_info(f"📦 {output}")

                        return_code = process.poll()
                        return return_code
                    except Exception as e:
                        smart_logger.log_error(f"❌ 安装过程异常: {e}")
                        return 1

                # 执行安装并显示进度
                return_code = run_install()
                install_time = time.time() - install_start

                if return_code == 0:
                    smart_logger.log_success(f"✅ 方案 {i+1} 安装成功 (耗时: {install_time:.1f}秒)")
                    smart_logger.log_info("🎉 PyTorch安装完成，开始验证...")

                    # 验证安装
                    smart_logger.log_info("🧪 验证安装结果...")
                    smart_logger.log_info("⏳ 正在重新加载PyTorch模块...")

                    try:
                        import importlib
                        # 清理模块缓存
                        modules_to_reload = ['torch', 'torchaudio']
                        for module in modules_to_reload:
                            if module in sys.modules:
                                del sys.modules[module]

                        smart_logger.log_info("📦 导入新版本PyTorch...")
                        import torch
                        new_version = torch.__version__
                        new_cuda = torch.version.cuda if hasattr(torch.version, 'cuda') else "N/A"
                        smart_logger.log_success(f"✅ 新版本加载成功: PyTorch {new_version}, CUDA {new_cuda}")

                        if torch.cuda.is_available():
                            gpu_name = torch.cuda.get_device_name(0)
                            smart_logger.log_info(f"🎮 检测到GPU: {gpu_name}")

                            # 测试50系显卡CUDA功能
                            smart_logger.log_info("🧪 步骤1: 测试基础CUDA功能...")

                            # 基础CUDA测试
                            test_tensor = torch.randn(100, 100).cuda()
                            result_tensor = test_tensor @ test_tensor.T
                            del test_tensor, result_tensor
                            torch.cuda.empty_cache()
                            smart_logger.log_success("✅ 基础CUDA测试通过")

                            # Demucs相关测试
                            smart_logger.log_info("🧪 步骤2: 测试Demucs CUDA兼容性...")
                            test_audio = torch.randn(1, 2, 44100).cuda()
                            conv_layer = torch.nn.Conv1d(2, 64, 7, padding=3).cuda()
                            conv_output = conv_layer(test_audio)
                            del test_audio, conv_layer, conv_output
                            torch.cuda.empty_cache()
                            smart_logger.log_success("✅ Demucs CUDA测试通过")

                            smart_logger.log_success("🎉 50系显卡CUDA兼容性修复成功")
                            smart_logger.log_success("🎉 Demucs GPU模式可正常使用")

                            # 记录修复结果
                            total_time = time.time() - start_time
                            smart_logger.log_info("=" * 80)
                            smart_logger.log_success("🎉 50系显卡CUDA兼容性修复完成")
                            smart_logger.log_info(f"📊 修复统计:")
                            smart_logger.log_info(f"   • 使用方案: {desc}")
                            smart_logger.log_info(f"   • 总耗时: {total_time:.1f}秒")
                            smart_logger.log_info(f"   • PyTorch版本: {new_version}")
                            smart_logger.log_info(f"   • CUDA版本: {new_cuda}")
                            smart_logger.log_success("✅ GPU模式和Demucs功能已恢复正常")
                            smart_logger.log_info("=" * 80)
                            return True
                        else:
                            smart_logger.log_warning("⚠️ CUDA不可用，但PyTorch安装成功")
                            if i < len(install_commands) - 1:
                                smart_logger.log_info("🔄 尝试下一个安装方案...")
                                continue
                            else:
                                smart_logger.log_info("💡 将使用CPU模式运行")
                                smart_logger.log_warning("⚠️ 50系显卡GPU功能未能修复，但程序可以CPU模式运行")
                                return True

                    except Exception as e:
                        smart_logger.log_error(f"❌ 验证失败: {e}")
                        if i < len(install_commands) - 1:
                            smart_logger.log_info("🔄 尝试下一个安装方案...")
                            continue
                        else:
                            smart_logger.log_error("❌ 所有方案验证都失败")
                            return False
                else:
                    smart_logger.log_error(f"❌ 方案 {i+1} 安装失败 (耗时: {install_time:.1f}秒)")
                    smart_logger.log_error(f"💥 返回码: {return_code}")
                    if i < len(install_commands) - 1:
                        smart_logger.log_info(f"🔄 尝试下一个方案 ({i+2}/{len(install_commands)})...")
                    continue

            except subprocess.TimeoutExpired:
                smart_logger.log_error(f"❌ 方案 {i+1} 安装超时")
                continue
            except Exception as e:
                smart_logger.log_error(f"❌ 方案 {i+1} 执行异常: {e}")
                continue

        # 所有方案都失败
        total_time = time.time() - start_time
        smart_logger.log_error("=" * 80)
        smart_logger.log_error("❌ 50系显卡CUDA兼容性修复失败")
        smart_logger.log_error(f"📊 修复统计:")
        smart_logger.log_error(f"   • 尝试方案数: {len(install_commands)}")
        smart_logger.log_error(f"   • 总耗时: {total_time:.1f}秒")
        smart_logger.log_error(f"   • 修复结果: 失败")
        smart_logger.log_error("=" * 80)
        return False

    except Exception as e:
        smart_logger.log_error(f"❌ PyTorch重新安装过程异常: {e}")

        # 修复方案2: 尝试更多PyTorch版本
        smart_logger.log_info("🔧 修复方案2: 尝试更多PyTorch版本组合")
        smart_logger.log_info("💡 原理: 尝试不同CUDA版本的PyTorch以找到兼容版本")

        additional_commands = [
            {
                "cmd": [sys.executable, "-m", "pip", "install", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/cu118"],
                "desc": "PyTorch Stable (CUDA 11.8) - 兼容性版本",
                "reason": "CUDA 11.8版本兼容性更好"
            },
            {
                "cmd": [sys.executable, "-m", "pip", "install", "--pre", "torch", "torchaudio", "--index-url", "https://download.pytorch.org/whl/nightly/cu118"],
                "desc": "PyTorch Nightly (CUDA 11.8) - 开发版本",
                "reason": "开发版本可能包含50系显卡修复"
            },
            {
                "cmd": [sys.executable, "-m", "pip", "install", "torch==2.1.0", "torchaudio==2.1.0", "--index-url", "https://download.pytorch.org/whl/cu121"],
                "desc": "PyTorch 2.1.0 (CUDA 12.1) - 特定版本",
                "reason": "该版本对新硬件支持较好"
            }
        ]

        for i, install_config in enumerate(additional_commands):
            cmd = install_config["cmd"]
            desc = install_config["desc"]
            reason = install_config["reason"]

            try:
                smart_logger.log_info(f"🔄 额外方案 {i+1}: {desc}")
                smart_logger.log_info(f"💡 选择原因: {reason}")

                # 卸载现有版本
                subprocess.run([sys.executable, "-m", "pip", "uninstall", "torch", "torchaudio", "-y"],
                              capture_output=True, text=True, timeout=120)

                install_start = time.time()
                return_code = subprocess.run(cmd, capture_output=True, text=True, timeout=600).returncode
                install_time = time.time() - install_start

                if return_code == 0:
                    smart_logger.log_success(f"✅ 额外方案 {i+1} 安装成功 (耗时: {install_time:.1f}秒)")

                    # 验证50系显卡兼容性
                    try:
                        import importlib
                        if 'torch' in sys.modules:
                            del sys.modules['torch']

                        import torch
                        if torch.cuda.is_available():
                            # 测试50系显卡CUDA功能
                            test_tensor = torch.randn(100, 100).cuda()
                            result_tensor = test_tensor @ test_tensor.T
                            del test_tensor, result_tensor
                            torch.cuda.empty_cache()

                            smart_logger.log_success("✅ 50系显卡CUDA兼容性修复成功")

                            total_time = time.time() - start_time
                            smart_logger.log_info("=" * 80)
                            smart_logger.log_success("🎉 50系显卡CUDA兼容性修复完成")
                            smart_logger.log_info(f"📊 修复统计:")
                            smart_logger.log_info(f"   • 使用方案: {desc}")
                            smart_logger.log_info(f"   • 总耗时: {total_time:.1f}秒")
                            smart_logger.log_info(f"   • PyTorch版本: {torch.__version__}")
                            smart_logger.log_success("✅ GPU模式和Demucs功能已恢复正常")
                            smart_logger.log_info("=" * 80)
                            return True

                    except Exception as e:
                        smart_logger.log_warning(f"⚠️ 额外方案 {i+1} 验证失败: {e}")
                        continue

            except Exception as e:
                smart_logger.log_error(f"❌ 额外方案 {i+1} 执行失败: {e}")
                continue

        # 修复方案3: 最后才考虑CPU模式
        smart_logger.log_warning("⚠️ 修复方案3: 所有GPU修复方案都失败，降级到CPU模式")
        smart_logger.log_info("💡 原理: 作为最后手段，使用CPU运行确保程序可用")

        try:
            os.environ['CUDA_VISIBLE_DEVICES'] = ''
            os.environ['FORCE_CPU'] = '1'

            total_time = time.time() - start_time
            smart_logger.log_info("=" * 80)
            smart_logger.log_warning("⚠️ 50系显卡CUDA兼容性修复: 降级到CPU模式")
            smart_logger.log_info(f"📊 修复统计:")
            smart_logger.log_info(f"   • 使用方案: CPU模式降级")
            smart_logger.log_info(f"   • 总耗时: {total_time:.1f}秒")
            smart_logger.log_info(f"   • GPU状态: 已禁用")
            smart_logger.log_warning("⚠️ 注意: CPU模式运行速度较慢，但功能完整")
            smart_logger.log_info("💡 建议: 更新NVIDIA驱动或等待PyTorch更新以获得GPU支持")
            smart_logger.log_info("=" * 80)
            return True

        except Exception as env_e:
            smart_logger.log_error(f"❌ CPU模式设置失败: {env_e}")

            # 最终失败记录
            total_time = time.time() - start_time
            smart_logger.log_error("=" * 80)
            smart_logger.log_error("❌ 50系显卡CUDA兼容性修复完全失败")
            smart_logger.log_error(f"📊 修复统计:")
            smart_logger.log_error(f"   • 尝试方案: 多版本PyTorch + CPU降级")
            smart_logger.log_error(f"   • 总耗时: {total_time:.1f}秒")
            smart_logger.log_error(f"   • 修复结果: 完全失败")
            smart_logger.log_error("📋 建议手动解决方案:")
            smart_logger.log_error("   1. 更新NVIDIA驱动到最新版本")
            smart_logger.log_error("   2. 手动安装PyTorch nightly版本")
            smart_logger.log_error("   3. 联系技术支持获取帮助")
            smart_logger.log_error("=" * 80)
            return False

# 执行50系显卡兼容性检查
smart_logger.log_info("🎮 开始50系显卡兼容性检查...")
smart_logger.log_info("🔍 正在检测GPU型号和CUDA兼容性...")

try:
    rtx50_compatible = check_and_fix_rtx50_cuda()

    if rtx50_compatible:
        smart_logger.log_success("✅ 50系显卡兼容性检查完成")
    else:
        smart_logger.log_warning("⚠️ 50系显卡兼容性问题未完全解决")
        smart_logger.log_info("💡 程序将尝试继续运行，可能使用CPU模式")

except Exception as e:
    smart_logger.log_error(f"❌ 50系显卡兼容性检查异常: {e}")
    smart_logger.log_info("💡 程序将继续启动，但可能存在兼容性问题")

# 启动智能日志监控
smart_logger.log_info("🔍 启动智能日志监控系统...")
log_analyzer.start_monitoring()

import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 配置日志系统
def cleanup_backup_logs():
    """清理旧的备用日志文件"""
    try:
        import glob
        backup_files = glob.glob('logs_backup_*.txt')
        if backup_files:
            smart_logger.log_info(f"🧹 发现 {len(backup_files)} 个备用日志文件，正在清理...")
            for backup_file in backup_files:
                try:
                    os.remove(backup_file)
                    smart_logger.log_success(f"已删除: {backup_file}")
                except Exception as e:
                    smart_logger.log_warning(f"无法删除 {backup_file}: {e}")
            smart_logger.log_success("备用日志文件清理完成")
    except Exception as e:
        smart_logger.log_error(f"清理备用日志文件时出错: {e}")

def setup_logging():
    """配置优化的日志系统（跨平台兼容，解决权限和编码问题）"""
    import tempfile
    import os

    # 启动时清理旧的备用日志文件
    try:
        cleanup_backup_logs()
    except:
        pass  # 忽略清理错误

    log_format = '%(asctime)s [%(levelname)s] %(message)s'
    handlers = []
    log_file_created = False

    # 尝试创建文件处理器 - 多种策略
    log_paths_to_try = [
        'logs.txt',  # 首选：当前目录
        os.path.join(tempfile.gettempdir(), 'indextts_logs.txt'),  # 备选：临时目录
        os.path.join(os.path.expanduser('~'), 'indextts_logs.txt'),  # 备选：用户目录
    ]

    for log_path in log_paths_to_try:
        try:
            # 确保目录存在
            log_dir = os.path.dirname(os.path.abspath(log_path))
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            # 尝试创建文件处理器
            file_handler = logging.FileHandler(log_path, encoding='utf-8', mode='a')
            handlers.append(file_handler)
            log_file_created = True

            # 安全的控制台输出（避免emoji编码错误）
            try:
                print(f"[INFO] 日志文件连接成功: {log_path}")
            except UnicodeEncodeError:
                print(f"[INFO] Log file connected: {log_path}")
            break

        except PermissionError:
            # 权限错误，尝试下一个路径
            continue
        except Exception:
            # 其他错误，尝试下一个路径
            continue

    # 如果所有路径都失败，输出安全的错误信息
    if not log_file_created:
        try:
            print("⚠️ 无法创建日志文件，程序将继续运行")
            print("💡 解决方案:")
            print("1. 以管理员身份运行程序")
            print("2. 检查文件权限设置")
            print("3. 确保磁盘空间充足")
        except UnicodeEncodeError:
            # 如果emoji无法显示，使用纯ASCII字符
            print("[WARNING] Cannot create log file, program will continue")
            print("[INFO] Solutions:")
            print("1. Run as administrator")
            print("2. Check file permissions")
            print("3. Ensure sufficient disk space")

    # 添加控制台处理器（使用stderr避免与程序输出混合）
    try:
        console_handler = logging.StreamHandler(sys.stderr)
        handlers.append(console_handler)
    except:
        # 如果stderr也有问题，使用stdout
        console_handler = logging.StreamHandler(sys.stdout)
        handlers.append(console_handler)

    # 配置日志系统
    try:
        logging.basicConfig(
            level=logging.INFO,
            format=log_format,
            handlers=handlers,
            force=True  # 强制重新配置
        )
    except Exception as e:
        # 最后的保险措施：最简单的日志配置
        try:
            print(f"[ERROR] Logging setup failed: {e}")
        except UnicodeEncodeError:
            print("[ERROR] Logging setup failed")

        # 使用最基本的日志配置
        logging.basicConfig(level=logging.INFO, force=True)

    return logging.getLogger(__name__)

def get_preset_audio_list():
    """获取预设样音列表"""
    preset_dir = "预设样音"
    if not os.path.exists(preset_dir):
        os.makedirs(preset_dir, exist_ok=True)
        return [], {}

    audio_extensions = ['.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg']
    audio_files = set()  # 使用set避免重复

    for ext in audio_extensions:
        audio_files.update(glob.glob(os.path.join(preset_dir, f"*{ext}")))
        audio_files.update(glob.glob(os.path.join(preset_dir, f"*{ext.upper()}")))

    # 创建文件名映射：显示名 -> 完整文件名
    file_mapping = {}
    display_names = []

    for file_path in sorted(audio_files):
        full_name = os.path.basename(file_path)
        display_name = os.path.splitext(full_name)[0]  # 去除后缀
        # 避免重复的显示名
        if display_name not in file_mapping:
            file_mapping[display_name] = full_name
            display_names.append(display_name)

    return display_names, file_mapping

def trim_audio_to_30s(input_path, output_path):
    """裁剪音频到前30秒，支持更长音频的上传"""
    try:
        import librosa
        import soundfile as sf

        print(f"🎵 开始处理音频文件: {os.path.basename(input_path)}")

        # 加载音频文件
        try:
            audio, sr = librosa.load(input_path, sr=None)
        except Exception as load_e:
            print(f"❌ 音频文件加载失败: {load_e}")
            print("💡 可能的原因: 文件格式不支持、文件损坏或路径包含特殊字符")
            return False

        original_duration = len(audio) / sr

        print(f"📊 音频信息: 时长 {original_duration:.1f}秒, 采样率 {sr}Hz")

        # 检查音频是否有效
        if len(audio) == 0:
            print("❌ 音频文件为空或无效")
            return False

        # 计算30秒对应的样本数
        max_samples = int(30 * sr)

        # 如果音频长度超过30秒，则裁剪到前30秒
        if len(audio) > max_samples:
            audio = audio[:max_samples]
            print(f"✂️ 音频已从 {original_duration:.1f}秒 裁剪到 30秒")
        else:
            print(f"✅ 音频长度 {original_duration:.1f}秒 在30秒内，保持原长度")

        # 保存裁剪后的音频
        try:
            sf.write(output_path, audio, sr)
            print(f"💾 音频已保存: {os.path.basename(output_path)}")
            return True
        except Exception as save_e:
            print(f"❌ 音频保存失败: {save_e}")
            return False

    except ImportError:
        print("⚠️ 需要安装librosa和soundfile库来支持音频裁剪功能")
        print("🔄 尝试直接复制文件...")
        # 如果没有安装相关库，直接复制文件
        try:
            shutil.copy2(input_path, output_path)
            print(f"📋 文件已复制: {os.path.basename(output_path)}")
            return True
        except Exception as copy_e:
            print(f"❌ 文件复制失败: {copy_e}")
            return False
    except Exception as e:
        print(f"❌ 音频处理过程中发生未知错误: {e}")
        print("🔄 尝试直接复制文件作为备选方案...")
        # 处理失败时，尝试直接复制
        try:
            shutil.copy2(input_path, output_path)
            print(f"📋 文件已复制（未经处理）: {os.path.basename(output_path)}")
            return True
        except Exception as copy_e:
            print(f"❌ 备选方案也失败: {copy_e}")
            return False

def upload_preset_audio(uploaded_file):
    """上传音频到预设样音文件夹"""
    if not uploaded_file:
        display_names, _ = get_preset_audio_list()
        return None, "❌ 请选择要上传的音频文件", ["请选择预设样音"] + display_names

    try:
        preset_dir = "预设样音"
        os.makedirs(preset_dir, exist_ok=True)

        # 获取文件名和扩展名
        file_name = os.path.basename(uploaded_file)
        name, ext = os.path.splitext(file_name)

        # 生成唯一文件名（如果文件已存在）
        counter = 1
        output_path = os.path.join(preset_dir, file_name)
        while os.path.exists(output_path):
            new_name = f"{name}_{counter}{ext}"
            output_path = os.path.join(preset_dir, new_name)
            counter += 1

        # 步骤1: 裁剪音频到30秒（支持更长音频上传）
        temp_cut_path = None
        if VOCAL_SEPARATION_AVAILABLE:
            # 如果有人声分离功能，先创建临时文件
            temp_cut_path = os.path.join(tempfile.gettempdir(), f"temp_cut_{int(time.time())}.wav")
            if not trim_audio_to_30s(uploaded_file, temp_cut_path):
                display_names, _ = get_preset_audio_list()
                return None, "❌ 音频裁剪失败", ["请选择预设样音"] + display_names

            # 步骤2: 人声分离
            try:
                print("🎵 正在进行人声分离，请稍候...")

                # 使用集成的人声分离器
                separator = VocalSeparator()
                success = separator.process_audio(temp_cut_path, output_path)
                separator.cleanup()

                # 清理临时文件
                if os.path.exists(temp_cut_path):
                    os.remove(temp_cut_path)

                if success:
                    uploaded_full_name = os.path.basename(output_path)
                    uploaded_display_name = os.path.splitext(uploaded_full_name)[0]
                    status_msg = f"✅ 音频上传成功（已进行人声分离）: {uploaded_full_name}"
                    print(status_msg)
                else:
                    raise Exception("人声分离处理失败")

            except Exception as vocal_e:
                print(f"⚠️ 人声分离失败，使用原始音频: {vocal_e}")
                # 如果人声分离失败，使用原始裁剪的音频
                if temp_cut_path and os.path.exists(temp_cut_path):
                    shutil.copy2(temp_cut_path, output_path)
                    os.remove(temp_cut_path)

                uploaded_full_name = os.path.basename(output_path)
                uploaded_display_name = os.path.splitext(uploaded_full_name)[0]
                status_msg = f"✅ 音频上传成功（人声分离失败，使用原始音频）: {uploaded_full_name}"
                print(status_msg)
        else:
            # 如果没有人声分离功能，直接裁剪保存
            if trim_audio_to_30s(uploaded_file, output_path):
                uploaded_full_name = os.path.basename(output_path)
                uploaded_display_name = os.path.splitext(uploaded_full_name)[0]
                status_msg = f"✅ 音频上传成功: {uploaded_full_name}"
                print(status_msg)
            else:
                display_names, _ = get_preset_audio_list()
                return None, "❌ 音频上传失败", ["请选择预设样音"] + display_names

        # 返回新的预设列表和选中的文件
        display_names, _ = get_preset_audio_list()
        return uploaded_display_name, status_msg, ["请选择预设样音"] + display_names

    except Exception as e:
        error_msg = f"❌ 上传失败: {str(e)}"
        print(error_msg)
        display_names, _ = get_preset_audio_list()
        return None, error_msg, ["请选择预设样音"] + display_names

def delete_preset_audio(selected_display_name):
    """删除选中的预设样音"""
    if not selected_display_name or selected_display_name == "请选择预设样音":
        display_names, _ = get_preset_audio_list()
        return "⚠️ 删除操作失败\n📝 原因: 请先选择要删除的样音", display_names, None

    try:
        preset_dir = "预设样音"
        display_names, file_mapping = get_preset_audio_list()

        # 通过显示名获取完整文件名
        if selected_display_name not in file_mapping:
            return f"❌ 删除操作失败\n📝 原因: 文件不存在\n📄 样音名: {selected_display_name}", display_names, selected_display_name

        full_file_name = file_mapping[selected_display_name]
        file_path = os.path.join(preset_dir, full_file_name)

        if os.path.exists(file_path):
            # 获取文件大小信息
            file_size = os.path.getsize(file_path)
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"

            os.remove(file_path)
            status_msg = f"🗑️ 样音删除成功！\n📄 文件名: {full_file_name}\n📊 释放空间: {size_str}"
            smart_logger.log_success(f"已删除样音: {full_file_name} (大小: {size_str})")

            # 返回新的预设列表
            new_display_names, _ = get_preset_audio_list()
            return status_msg, new_display_names, None
        else:
            return f"❌ 删除操作失败\n📝 原因: 文件不存在\n📄 文件名: {full_file_name}", display_names, selected_display_name

    except Exception as e:
        error_msg = f"❌ 删除操作失败\n📝 原因: {str(e)}\n📄 样音名: {selected_display_name}"
        smart_logger.log_error(f"删除样音失败: {e}")
        display_names, _ = get_preset_audio_list()
        return error_msg, display_names, selected_display_name

def parse_subtitle_file(file_path):
    """解析字幕文件，提取纯文本内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == '.srt':
            # 解析SRT格式
            import re
            # 移除序号和时间戳，只保留文本
            text_lines = []
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                # 跳过序号行和时间戳行
                if line and not line.isdigit() and '-->' not in line:
                    text_lines.append(line)
            return '\n'.join(text_lines)

        elif file_ext == '.vtt':
            # 解析VTT格式
            import re
            lines = content.split('\n')
            text_lines = []
            skip_header = True
            for line in lines:
                line = line.strip()
                if skip_header and line.startswith('WEBVTT'):
                    continue
                skip_header = False
                # 跳过时间戳行和空行
                if line and '-->' not in line and not line.startswith('NOTE'):
                    text_lines.append(line)
            return '\n'.join(text_lines)

        elif file_ext == '.txt':
            # 直接返回文本内容
            return content.strip()

        else:
            return None

    except Exception as e:
        print(f"解析字幕文件失败: {e}")
        return None

def clean_text_for_subtitle(text):
    """清除文本中的所有符号，包括标点符号、特殊符号和空格"""
    import re

    # 定义需要清除的符号模式
    # 包括中文标点、英文标点、特殊符号、空格等
    symbols_pattern = r'[，。！？；：""''（）【】《》〈〉「」『』〔〕［］｛｝〖〗〘〙〚〛,.!?;:"\'()\[\]{}<>""''—–\-_+=*&^%$#@~`|\\\/\s]'

    # 清除所有符号和空格
    cleaned_text = re.sub(symbols_pattern, '', text)

    return cleaned_text.strip()

def split_text_into_segments(text):
    """将文本按标点符号和语义单元智能拆分成短句"""
    import re

    # 定义主要断句标点符号（按优先级排序）
    primary_punctuation = ['。', '！', '？']
    secondary_punctuation = ['，', '；', '：']

    segments = []

    # 首先按主要标点符号分割
    primary_parts = []
    current_part = ""

    for char in text:
        current_part += char
        if char in primary_punctuation:
            if current_part.strip():
                primary_parts.append(current_part.strip())
            current_part = ""

    # 添加剩余部分
    if current_part.strip():
        primary_parts.append(current_part.strip())

    # 对每个主要部分进行二次分割
    for part in primary_parts:
        if not part:
            continue

        # 按次要标点符号进一步分割
        sub_segments = []
        current_segment = ""

        for char in part:
            current_segment += char
            if char in secondary_punctuation:
                if current_segment.strip():
                    sub_segments.append(current_segment.strip())
                current_segment = ""

        # 添加剩余部分
        if current_segment.strip():
            sub_segments.append(current_segment.strip())

        # 如果没有次要标点符号，直接使用原部分
        if not sub_segments:
            sub_segments = [part]

        # 进一步处理过长的段落（超过15个字符的进行语义拆分）
        for segment in sub_segments:
            if len(segment) > 15:
                # 尝试按语义单元拆分（寻找合适的分割点）
                semantic_split = split_by_semantic_units(segment)
                segments.extend(semantic_split)
            else:
                segments.append(segment)

    return segments

def split_by_semantic_units(text):
    """按语义单元拆分长句子"""
    # 定义语义分割的关键词和模式
    semantic_patterns = [
        r'(.*?)(的|地|得)([^的地得]{3,})',  # "的/地/得" 结构
        r'(.*?)(在|从|向|到|对|为|被|把|让|使)([^在从向到对为被把让使]{3,})',  # 介词结构
        r'(.*?)(而且|但是|然而|因为|所以|如果|虽然|尽管)([^而且但是然而因为所以如果虽然尽管]{3,})',  # 连词结构
        r'(.*?)(就是|就会|就能|就要|就可以)([^就]{3,})',  # "就" 结构
    ]

    segments = []
    remaining_text = text

    # 尝试语义分割
    for pattern in semantic_patterns:
        import re
        match = re.search(pattern, remaining_text)
        if match:
            groups = match.groups()
            for group in groups:
                if group and len(group.strip()) > 2:
                    segments.append(group.strip())
            return segments

    # 如果语义分割失败，按字符数强制分割
    if len(text) > 15:
        mid_point = len(text) // 2
        # 寻找最近的合适分割点
        for i in range(mid_point - 3, mid_point + 4):
            if i < len(text) and text[i] in '的地得在从向到对为被把让使':
                segments.append(text[:i+1])
                segments.append(text[i+1:])
                return segments

        # 如果找不到合适分割点，直接从中间分割
        segments.append(text[:mid_point])
        segments.append(text[mid_point:])
    else:
        segments.append(text)

    return segments

def generate_subtitle_from_audio(audio_data, text_content):
    """基于音频和文本生成断句格式的SRT字幕文件（清除符号）"""
    try:
        import librosa
        import numpy as np

        # 处理Gradio Audio组件返回的数据格式
        if audio_data is None:
            return None

        # Gradio Audio返回格式: (sample_rate, audio_array) 或 文件路径字符串
        if isinstance(audio_data, str):
            # 如果是文件路径
            duration = librosa.get_duration(filename=audio_data)
        elif isinstance(audio_data, tuple) and len(audio_data) == 2:
            # 如果是(sample_rate, audio_array)格式
            sample_rate, audio_array = audio_data
            if isinstance(audio_array, np.ndarray):
                # 计算音频时长：样本数 / 采样率
                duration = len(audio_array) / sample_rate
            else:
                return None
        else:
            return None

        # 智能拆分文本为短句
        text_segments = split_text_into_segments(text_content)

        if not text_segments:
            return None

        # 清除每个段落的符号
        cleaned_segments = []
        for segment in text_segments:
            cleaned_segment = clean_text_for_subtitle(segment)
            if cleaned_segment:  # 只保留非空的清理后文本
                cleaned_segments.append(cleaned_segment)

        if not cleaned_segments:
            return None

        # 计算每个短句的时间分配（根据音频时长和段落数量）
        time_per_segment = duration / len(cleaned_segments) if cleaned_segments else duration

        # 生成SRT内容
        srt_content = []
        for i, segment in enumerate(cleaned_segments):
            start_time = i * time_per_segment
            end_time = (i + 1) * time_per_segment

            # 格式化时间戳
            start_h, start_m, start_s = int(start_time // 3600), int((start_time % 3600) // 60), start_time % 60
            end_h, end_m, end_s = int(end_time // 3600), int((end_time % 3600) // 60), end_time % 60

            start_timestamp = f"{start_h:02d}:{start_m:02d}:{start_s:06.3f}".replace('.', ',')
            end_timestamp = f"{end_h:02d}:{end_m:02d}:{end_s:06.3f}".replace('.', ',')

            srt_content.append(f"{i + 1}")
            srt_content.append(f"{start_timestamp} --> {end_timestamp}")
            srt_content.append(segment)
            srt_content.append("")  # 空行

        # 保存SRT文件
        timestamp = int(time.time())
        srt_filename = f"subtitle_{timestamp}.srt"
        srt_path = os.path.join("outputs", srt_filename)

        os.makedirs("outputs", exist_ok=True)
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(srt_content))

        print(f"✅ 生成断句字幕文件: {srt_filename}")
        print(f"📊 原始文本长度: {len(text_content)} 字符")
        print(f"📊 拆分段落数量: {len(cleaned_segments)} 个")
        print(f"📊 音频时长: {duration:.2f} 秒")
        print(f"📊 平均每段时长: {time_per_segment:.2f} 秒")

        return srt_path

    except Exception as e:
        print(f"生成字幕失败: {e}")
        return None

def clear_audio_cache():
    """清除音频缓存、字幕文件和临时文件（包括系统临时目录中的音频处理缓存）"""
    try:
        deleted_count = 0
        deleted_size = 0
        deleted_details = []

        # 清除outputs文件夹（包括音频和字幕文件）
        outputs_dir = "outputs"
        outputs_audio_files = []
        outputs_subtitle_files = []
        if os.path.exists(outputs_dir):
            for file_name in os.listdir(outputs_dir):
                file_path = os.path.join(outputs_dir, file_name)
                if os.path.isfile(file_path):
                    try:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        deleted_count += 1
                        deleted_size += file_size

                        # 分类统计文件类型
                        if file_name.lower().endswith(('.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg')):
                            outputs_audio_files.append(file_name)
                        elif file_name.lower().endswith(('.srt', '.vtt', '.txt')):
                            outputs_subtitle_files.append(file_name)

                        print(f"已删除: {file_path}")
                    except Exception as e:
                        print(f"删除文件失败 {file_path}: {e}")

        # 统计outputs文件夹清理结果
        if outputs_audio_files or outputs_subtitle_files:
            audio_count = len(outputs_audio_files)
            subtitle_count = len(outputs_subtitle_files)
            total_outputs = audio_count + subtitle_count

            detail_parts = []
            if audio_count > 0:
                detail_parts.append(f"音频{audio_count}个")
            if subtitle_count > 0:
                detail_parts.append(f"字幕{subtitle_count}个")

            deleted_details.append(f"📁 outputs文件夹: {total_outputs}个文件({', '.join(detail_parts)})")

        # 清除prompts文件夹中的临时文件
        prompts_dir = "prompts"
        prompts_files = []
        if os.path.exists(prompts_dir):
            for file_name in os.listdir(prompts_dir):
                file_path = os.path.join(prompts_dir, file_name)
                if os.path.isfile(file_path):
                    try:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        deleted_count += 1
                        deleted_size += file_size
                        prompts_files.append(file_name)
                        print(f"已删除: {file_path}")
                    except Exception as e:
                        print(f"删除文件失败 {file_path}: {e}")

        if prompts_files:
            deleted_details.append(f"📁 prompts文件夹: {len(prompts_files)}个文件")

        # 清除系统临时目录中的音频处理缓存文件
        system_temp_files = []
        try:
            # 获取系统临时目录（通用路径，适用于不同用户名）
            import tempfile
            system_temp_dir = tempfile.gettempdir()
            print(f"🔍 检查系统临时目录: {system_temp_dir}")

            if os.path.exists(system_temp_dir):
                # 定义音频处理相关的临时文件模式
                audio_temp_patterns = [
                    # 音频裁剪临时文件
                    "temp_cut_*.wav", "temp_cut_*.mp3", "temp_cut_*.flac", "temp_cut_*.m4a",
                    "tmp_cut_*.wav", "tmp_cut_*.mp3", "tmp_cut_*.flac", "tmp_cut_*.m4a",

                    # 人声分离临时文件
                    "vocals_*.wav", "vocals_*.mp3", "vocals_*.flac", "vocals_*.m4a",
                    "instrumental_*.wav", "instrumental_*.mp3", "instrumental_*.flac", "instrumental_*.m4a",
                    "separated_*.wav", "separated_*.mp3", "separated_*.flac", "separated_*.m4a",

                    # 音频处理通用临时文件
                    "audio_temp_*.wav", "audio_temp_*.mp3", "audio_temp_*.flac", "audio_temp_*.m4a",
                    "temp_audio_*.wav", "temp_audio_*.mp3", "temp_audio_*.flac", "temp_audio_*.m4a",
                    "processed_*.wav", "processed_*.mp3", "processed_*.flac", "processed_*.m4a",

                    # Gradio和其他库产生的音频临时文件
                    "gradio_*.wav", "gradio_*.mp3", "gradio_*.flac", "gradio_*.m4a",
                    "tmp*.wav", "tmp*.mp3", "tmp*.flac", "tmp*.m4a",

                    # 人声分离库特定的临时文件
                    "demucs_*.wav", "demucs_*.mp3", "demucs_*.flac", "demucs_*.m4a",
                    "uvr_*.wav", "uvr_*.mp3", "uvr_*.flac", "uvr_*.m4a",
                    "spleeter_*.wav", "spleeter_*.mp3", "spleeter_*.flac", "spleeter_*.m4a",

                    # 音频转换临时文件
                    "convert_*.wav", "convert_*.mp3", "convert_*.flac", "convert_*.m4a",
                    "resample_*.wav", "resample_*.mp3", "resample_*.flac", "resample_*.m4a",

                    # IndexTTS特定的临时文件
                    "index_tts_*.wav", "index_tts_*.mp3", "index_tts_*.flac",
                    "tts_temp_*.wav", "tts_temp_*.mp3", "tts_temp_*.flac",

                    # 更通用的临时文件模式
                    "temp_*.wav", "temp_*.mp3", "temp_*.flac", "temp_*.m4a", "temp_*.aac", "temp_*.ogg",
                    "tmp_*.wav", "tmp_*.mp3", "tmp_*.flac", "tmp_*.m4a", "tmp_*.aac", "tmp_*.ogg"
                ]

                # 遍历系统临时目录查找匹配的文件
                for file_name in os.listdir(system_temp_dir):
                    file_path = os.path.join(system_temp_dir, file_name)
                    if os.path.isfile(file_path):
                        # 检查文件是否匹配音频处理临时文件模式
                        is_audio_temp = False
                        for pattern in audio_temp_patterns:
                            # 简单的通配符匹配
                            pattern_regex = pattern.replace('*', '.*')
                            if re.match(pattern_regex, file_name, re.IGNORECASE):
                                is_audio_temp = True
                                break

                        # 额外检查：包含特定关键词的音频文件
                        if not is_audio_temp and file_name.lower().endswith(('.wav', '.mp3', '.flac', '.m4a', '.aac', '.ogg')):
                            audio_keywords = [
                                'temp', 'tmp', 'cut', 'vocal', 'instrumental', 'separated', 'processed',
                                'gradio', 'demucs', 'uvr', 'spleeter', 'convert', 'resample', 'audio',
                                'index_tts', 'tts_temp', 'cache', 'buffer', 'output', 'result'
                            ]
                            if any(keyword in file_name.lower() for keyword in audio_keywords):
                                is_audio_temp = True

                        if is_audio_temp:
                            try:
                                # 检查文件修改时间，删除10分钟以上的文件（降低时间阈值，提高清理效果）
                                file_mtime = os.path.getmtime(file_path)
                                current_time = time.time()
                                file_age_minutes = (current_time - file_mtime) / 60

                                # 降低时间阈值到10分钟，但增加更多安全检查
                                if file_age_minutes > 10:  # 10分钟 = 600秒
                                    # 额外安全检查：确保文件不是正在被使用
                                    try:
                                        # 尝试以独占模式打开文件，如果失败说明文件正在被使用
                                        with open(file_path, 'r+b') as test_file:
                                            pass

                                        file_size = os.path.getsize(file_path)
                                        os.remove(file_path)
                                        deleted_count += 1
                                        deleted_size += file_size
                                        system_temp_files.append(file_name)
                                        print(f"已删除系统临时文件: {file_path} (创建于 {file_age_minutes:.1f} 分钟前)")

                                    except (PermissionError, IOError):
                                        print(f"跳过正在使用的文件: {file_name}")
                                else:
                                    print(f"跳过最近创建的文件: {file_name} (创建于 {file_age_minutes:.1f} 分钟前)")
                            except Exception as e:
                                print(f"删除系统临时文件失败 {file_path}: {e}")

        except Exception as e:
            print(f"清理系统临时目录时出错: {e}")

        if system_temp_files:
            deleted_details.append(f"🗂️ 系统临时目录: {len(system_temp_files)}个音频缓存文件")

        # 清除可能的临时音频和字幕文件
        temp_patterns = [
            "temp_*.wav", "temp_*.mp3", "tmp_*.wav", "tmp_*.mp3",
            "cache_*.wav", "cache_*.mp3", "*.tmp",
            "subtitle_*.srt", "temp_*.srt", "tmp_*.srt"  # 添加字幕文件模式
        ]

        temp_files = []
        for pattern in temp_patterns:
            for file_path in glob.glob(pattern):
                if os.path.isfile(file_path):
                    try:
                        file_size = os.path.getsize(file_path)
                        os.remove(file_path)
                        deleted_count += 1
                        deleted_size += file_size
                        temp_files.append(os.path.basename(file_path))
                        print(f"已删除临时文件: {file_path}")
                    except Exception as e:
                        print(f"删除临时文件失败 {file_path}: {e}")

        if temp_files:
            deleted_details.append(f"🗂️ 临时文件: {len(temp_files)}个文件")

        # 格式化文件大小
        if deleted_size < 1024:
            size_str = f"{deleted_size} B"
        elif deleted_size < 1024 * 1024:
            size_str = f"{deleted_size / 1024:.1f} KB"
        else:
            size_str = f"{deleted_size / (1024 * 1024):.1f} MB"

        if deleted_count > 0:
            details_str = "<br>".join(deleted_details)
            status_msg = f"✅ 缓存清理完成！<br><br>📊 清理详情:<br>{details_str}<br><br>📈 总计: {deleted_count}个文件，释放空间 {size_str}"
            smart_logger.log_success(f"缓存清理完成: {deleted_count}个文件，释放空间 {size_str}")
        else:
            status_msg = "💡 没有找到需要清理的缓存文件<br><br>📁 检查的位置:<br>📁 outputs文件夹(音频+字幕)<br>📁 prompts文件夹<br>🗂️ 项目根目录临时文件<br>🗂️ 系统临时目录(音频处理缓存)"
            smart_logger.log_info("缓存清理: 没有找到需要清理的文件")

        print(status_msg.replace("<br>", "\n"))
        return status_msg

    except Exception as e:
        error_msg = f"❌ 缓存清理失败: {str(e)}"
        smart_logger.log_error(f"缓存清理失败: {e}")
        print(error_msg)
        return error_msg

# ==================== 人声分离功能 ====================

class VocalSeparator:
    """人声分离器 - 集成到webui.py中"""

    def __init__(self):
        """初始化人声分离器"""
        if not VOCAL_SEPARATION_AVAILABLE:
            raise ImportError("人声分离依赖未安装")

        self.temp_dir = tempfile.mkdtemp()
        print(f"🎵 人声分离器初始化完成，临时目录: {self.temp_dir}")

    def cut_audio_30s(self, audio_path: str, output_path: str) -> bool:
        """将音频切割为30秒，支持更长音频的上传"""
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=None)
            original_duration = len(y) / sr

            print(f"📊 音频信息: 时长 {original_duration:.1f}秒, 采样率 {sr}Hz")

            # 计算30秒对应的样本数
            duration_samples = min(30 * sr, len(y))

            # 切割音频（取前30秒）
            y_cut = y[:duration_samples]

            if original_duration > 30:
                print(f"✂️ 音频已从 {original_duration:.1f}秒 裁剪到 30秒")
            else:
                print(f"✅ 音频长度 {original_duration:.1f}秒 在30秒内，保持原长度")

            # 保存切割后的音频
            sf.write(output_path, y_cut, sr)

            print(f"💾 音频已保存: {output_path}")
            return True

        except Exception as e:
            print(f"❌ 音频切割失败: {e}")
            print("💡 可能的原因:")
            print("   1. 音频文件格式不支持")
            print("   2. 文件损坏或路径包含特殊字符")
            print("   3. 磁盘空间不足")
            return False

    def separate_vocals(self, audio_path: str) -> tuple:
        """根据可用方法进行人声分离"""
        try:
            print(f"🎵 开始人声分离 (方法: {VOCAL_SEPARATION_METHOD})...")

            if VOCAL_SEPARATION_METHOD == "demucs":
                print("🚀 使用Demucs - 最先进的人声分离技术")
                return self._separate_with_demucs(audio_path)
            elif VOCAL_SEPARATION_METHOD == "audio_separator":
                print("🎵 使用audio-separator - 高质量人声分离")
                return self._separate_with_audio_separator(audio_path)
            else:
                print("⚠️ 使用librosa - 基础人声分离")
                return self._simple_effective_separation(audio_path)

        except Exception as e:
            print(f"❌ 主要分离方法失败: {e}")
            print("🔄 尝试使用简单有效的备用方法...")
            return self._simple_effective_separation(audio_path)

    def _separate_with_audio_separator(self, audio_path: str) -> tuple:
        """使用audio-separator进行人声分离"""
        try:
            # 使用全局存储的Separator类
            if 'AudioSeparator' not in globals():
                raise ImportError("AudioSeparator not available")

            Separator = globals()['AudioSeparator']
            print("🔄 使用audio-separator进行人声分离...")

            # 创建分离器实例，使用高质量模型
            separator = Separator(
                model_name='UVR-MDX-NET-Inst_HQ_3',  # 使用高质量模型
                output_dir=self.temp_dir,
                output_format='wav',
                normalization_threshold=0.9,
                amplification_threshold=0.6,
                mdx_segment_size=512,  # 增大分段大小提升质量
                mdx_overlap=0.5,      # 增大重叠率提升质量
                mdx_batch_size=1,
                use_cuda=False,
                use_cpu=True,
                denoise=True,
                keep_cache=False,
                # 添加高质量参数
                mdx_hop_length=1024,
                mdx_enable_denoise=True
            )

            # 执行分离
            print("📥 首次使用会下载模型文件，请耐心等待...")
            output_files = separator.separate(audio_path)

            # 查找人声和伴奏文件
            vocals_file = None
            instrumental_file = None

            for file_path in output_files:
                filename = os.path.basename(file_path).lower()
                if 'vocals' in filename:
                    vocals_file = file_path
                elif 'instrumental' in filename:
                    instrumental_file = file_path

            if not vocals_file:
                raise ValueError("未找到人声分离结果")

            print(f"✅ audio-separator分离完成: {vocals_file}")
            return vocals_file, instrumental_file

        except Exception as e:
            print(f"❌ audio-separator分离失败: {e}")
            raise

    def _separate_with_demucs(self, audio_path: str) -> tuple:
        """使用Demucs进行高质量人声分离"""
        try:
            print("🚀 使用Demucs进行专业级人声分离...")
            print("💡 Demucs是Facebook Research开发的最先进开源人声分离技术")

            # 检查Demucs是否可用
            if 'demucs' not in globals():
                raise ImportError("Demucs not available")

            demucs = globals()['demucs']
            demucs_pretrained = globals()['demucs_pretrained']
            torch = globals()['torch']

            # 检测设备
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"🔧 使用设备: {device}")

            # 加载预训练模型
            print("📥 加载Demucs模型...")
            model = demucs_pretrained.get_model('htdemucs')
            model.to(device)
            model.eval()

            # 加载音频
            print("🔄 正在执行人声分离，这可能需要几分钟...")
            wav, sr = librosa.load(audio_path, sr=44100, mono=False)

            # 确保是立体声
            if len(wav.shape) == 1:
                wav = np.stack([wav, wav])

            # 转换为torch tensor
            wav_tensor = torch.from_numpy(wav).float().unsqueeze(0).to(device)

            # 执行分离 - 使用apply_model函数
            try:
                # 动态导入demucs.apply模块
                import importlib
                apply_module = importlib.import_module('demucs.apply')
                apply_model = apply_module.apply_model
            except ImportError:
                # 如果demucs.apply不可用，尝试其他方式
                try:
                    from demucs.separate import apply_model  # type: ignore
                except ImportError:
                    # 最后尝试直接调用模型
                    def apply_model(model, wav, device=None):  # type: ignore
                        _ = device  # 忽略device参数
                        return model(wav)

            with torch.no_grad():
                sources = apply_model(model, wav_tensor, device=device)

            # 提取各个音轨 (htdemucs输出顺序: drums, bass, other, vocals)
            drums = sources[0, 0].cpu().numpy()
            bass = sources[0, 1].cpu().numpy()
            other = sources[0, 2].cpu().numpy()
            vocals = sources[0, 3].cpu().numpy()

            # 转换为单声道
            if len(vocals.shape) > 1:
                vocals = np.mean(vocals, axis=0)
            if len(drums.shape) > 1:
                drums = np.mean(drums, axis=0)
            if len(bass.shape) > 1:
                bass = np.mean(bass, axis=0)
            if len(other.shape) > 1:
                other = np.mean(other, axis=0)

            # 组合伴奏（除人声外的所有音轨）
            instrumental = drums + bass + other

            # 保存文件
            vocals_path = os.path.join(self.temp_dir, "vocals_demucs.wav")
            instrumental_path = os.path.join(self.temp_dir, "instrumental_demucs.wav")

            # 保存高质量音频
            sf.write(vocals_path, vocals, sr, subtype='PCM_24')
            sf.write(instrumental_path, instrumental, sr, subtype='PCM_24')

            print(f"✅ Demucs专业级分离完成: {vocals_path}")
            print(f"📊 人声质量: 专业级 | 背景去除: 彻底")

            return vocals_path, instrumental_path

        except Exception as e:
            print(f"❌ Demucs分离失败: {e}")
            print("🔄 将尝试使用备用分离方法...")
            import traceback
            traceback.print_exc()
            raise

    def _separate_with_librosa(self, audio_path: str) -> tuple:
        """使用librosa进行专业级人声分离"""
        try:
            print("🔄 使用librosa进行专业级人声分离...")

            # 加载音频，保持原始采样率
            y, sr = librosa.load(audio_path, sr=None, mono=False)

            # 确保是立体声，如果是单声道则复制
            if len(y.shape) == 1:
                y = np.array([y, y])

            # 如果是立体声，先尝试立体声分离
            if len(y.shape) == 2 and y.shape[0] == 2:
                vocals_stereo = self._stereo_vocal_separation(y, sr)
                if vocals_stereo is not None:
                    vocals_path = os.path.join(self.temp_dir, "vocals_stereo.wav")
                    instrumental_path = os.path.join(self.temp_dir, "instrumental_stereo.wav")

                    # 确保长度匹配
                    min_length = min(y.shape[1], vocals_stereo.shape[1])
                    y_trimmed = y[:, :min_length]
                    vocals_trimmed = vocals_stereo[:, :min_length]

                    # 计算伴奏
                    instrumental_stereo = y_trimmed - vocals_trimmed

                    # 保存结果
                    sf.write(vocals_path, vocals_trimmed.T, sr, subtype='PCM_24')
                    sf.write(instrumental_path, instrumental_stereo.T, sr, subtype='PCM_24')

                    print(f"✅ 立体声分离完成: {vocals_path}")
                    return vocals_path, instrumental_path

            # 转换为单声道进行高级分离
            y_mono = librosa.to_mono(y)
            original_length = len(y_mono)

            # 使用多种方法组合分离
            vocals_final = self._advanced_vocal_separation(y_mono, sr)

            # 确保长度一致
            if len(vocals_final) != original_length:
                if len(vocals_final) > original_length:
                    vocals_final = vocals_final[:original_length]
                else:
                    padding = original_length - len(vocals_final)
                    vocals_final = np.pad(vocals_final, (0, padding), mode='constant')

            # 计算伴奏
            instrumental_final = y_mono - vocals_final

            # 应用后处理
            vocals_final = self._advanced_post_process(vocals_final, sr)

            # 保存分离结果
            vocals_path = os.path.join(self.temp_dir, "vocals_librosa.wav")
            instrumental_path = os.path.join(self.temp_dir, "instrumental_librosa.wav")

            # 使用高质量参数保存
            sf.write(vocals_path, vocals_final, sr, subtype='PCM_24')
            sf.write(instrumental_path, instrumental_final, sr, subtype='PCM_24')

            print(f"✅ librosa专业级分离完成: {vocals_path}")
            return vocals_path, instrumental_path

        except Exception as e:
            print(f"❌ librosa分离失败: {e}")
            import traceback
            traceback.print_exc()
            # 最后的备用方法
            return self._simple_vocal_extraction(audio_path)

    def _stereo_vocal_separation(self, y_stereo, sr):
        """立体声人声分离"""
        try:
            print("🎵 执行立体声人声分离...")

            left = y_stereo[0]
            right = y_stereo[1]

            # 方法1: 中心声道提取
            center = (left + right) / 2  # type: ignore
            side = (left - right) / 2  # type: ignore
            _ = center, side  # 保留变量以备后用

            # 方法2: 使用STFT进行频域分离
            hop_length = 512
            n_fft = 2048

            # 对左右声道分别进行STFT
            S_left = librosa.stft(left, hop_length=hop_length, n_fft=n_fft)
            S_right = librosa.stft(right, hop_length=hop_length, n_fft=n_fft)

            # 计算相位差和幅度差
            phase_diff = np.angle(S_left) - np.angle(S_right)
            magnitude_ratio = np.abs(S_left) / (np.abs(S_right) + 1e-10)

            # 创建人声掩码（中心声道通常相位差小）
            phase_mask = np.abs(phase_diff) < np.pi/4  # 相位差小于45度
            magnitude_mask = (magnitude_ratio > 0.5) & (magnitude_ratio < 2.0)  # 幅度比接近1

            # 组合掩码
            vocal_mask = phase_mask & magnitude_mask

            # 应用掩码提取人声
            S_vocal_left = S_left * vocal_mask
            S_vocal_right = S_right * vocal_mask

            # 重构人声，指定原始长度
            original_length = len(left)
            vocal_left = librosa.istft(S_vocal_left, hop_length=hop_length, length=original_length)
            vocal_right = librosa.istft(S_vocal_right, hop_length=hop_length, length=original_length)

            # 组合立体声人声
            vocals_stereo = np.array([vocal_left, vocal_right])

            # 确保长度与原始音频完全一致
            target_length = y_stereo.shape[1]
            if vocals_stereo.shape[1] != target_length:
                if vocals_stereo.shape[1] > target_length:
                    vocals_stereo = vocals_stereo[:, :target_length]
                else:
                    # 如果太短，用零填充
                    padding = target_length - vocals_stereo.shape[1]
                    vocals_stereo = np.pad(vocals_stereo, ((0, 0), (0, padding)), mode='constant')

            return vocals_stereo

        except Exception as e:
            print(f"⚠️ 立体声分离失败: {e}")
            return None

    def _advanced_vocal_separation(self, y_mono, sr):
        """高级单声道人声分离"""
        try:
            print("🎵 执行高级单声道人声分离...")

            # 使用更高质量的STFT参数
            hop_length = 256  # 更小的hop_length提升时间分辨率
            win_length = 1024
            n_fft = 2048

            # 计算STFT
            S_full = librosa.stft(y_mono, hop_length=hop_length, win_length=win_length, n_fft=n_fft)
            magnitude = np.abs(S_full)
            phase = np.angle(S_full)

            # 方法1: 改进的谐波-冲击分离
            S_harmonic, S_percussive = librosa.decompose.hpss(magnitude, margin=(1.0, 5.0))
            _ = S_percussive  # 保留变量以备后用

            # 方法2: 使用中值滤波进行背景分离
            S_background = librosa.decompose.nn_filter(magnitude,
                                                     aggregate=np.median,
                                                     metric='cosine',
                                                     width=int(librosa.frames_to_time(magnitude.shape[1], sr=sr)))
            S_background = np.minimum(magnitude, S_background)

            # 方法3: 频率掩码 - 人声主要在特定频率范围
            freq_bins = librosa.fft_frequencies(sr=sr, n_fft=n_fft)
            vocal_freq_mask = (freq_bins >= 80) & (freq_bins <= 8000)  # 人声频率范围

            # 创建频率掩码矩阵
            freq_mask_matrix = np.zeros_like(magnitude)
            freq_mask_matrix[vocal_freq_mask, :] = 1.0

            # 方法4: 时间连续性分析
            # 人声通常有更强的时间连续性
            magnitude_diff = np.diff(magnitude, axis=1)
            continuity_score = np.exp(-np.abs(magnitude_diff))
            continuity_mask = np.zeros_like(magnitude)
            continuity_mask[:, 1:] = continuity_score
            continuity_mask[:, 0] = continuity_mask[:, 1]

            # 组合多种方法
            # 基础人声估计
            S_vocals_base = S_harmonic - 0.3 * S_background

            # 应用频率掩码
            S_vocals_freq = S_vocals_base * freq_mask_matrix

            # 应用连续性掩码
            S_vocals_cont = S_vocals_freq * (0.7 + 0.3 * continuity_mask)

            # 创建软掩码
            mask_vocals = librosa.util.softmask(S_vocals_cont,
                                              2.0 * (magnitude - S_vocals_cont),
                                              power=2)

            # 应用掩码
            S_vocals_final = mask_vocals * magnitude

            # 重构音频，确保长度匹配
            S_vocals_complex = S_vocals_final * np.exp(1j * phase)
            vocals = librosa.istft(S_vocals_complex,
                                 hop_length=hop_length,
                                 win_length=win_length,
                                 length=len(y_mono))

            # 确保输出长度与输入完全一致
            if len(vocals) != len(y_mono):
                if len(vocals) > len(y_mono):
                    vocals = vocals[:len(y_mono)]
                else:
                    # 用零填充
                    padding = len(y_mono) - len(vocals)
                    vocals = np.pad(vocals, (0, padding), mode='constant')

            return vocals

        except Exception as e:
            print(f"⚠️ 高级分离失败: {e}")
            # 回退到简单方法
            return self._basic_vocal_separation(y_mono, sr)

    def _basic_vocal_separation(self, y_mono, sr):
        """基础人声分离方法"""
        try:
            print("🎵 执行基础人声分离...")

            # 使用简单的谐波分离
            S_full = librosa.stft(y_mono)
            magnitude = np.abs(S_full)
            phase = np.angle(S_full)

            # 谐波-冲击分离
            S_harmonic, S_percussive = librosa.decompose.hpss(magnitude)
            _ = S_percussive  # 保留变量以备后用

            # 人声主要在谐波部分
            S_vocals = S_harmonic * 0.8  # 保留80%的谐波成分

            # 重构
            S_vocals_complex = S_vocals * np.exp(1j * phase)
            vocals = librosa.istft(S_vocals_complex, length=len(y_mono))

            return vocals

        except Exception as e:
            print(f"⚠️ 基础分离也失败: {e}")
            return y_mono  # 返回原音频

    def _advanced_post_process(self, vocals, sr):
        """高级人声后处理，大幅提升质量"""
        try:
            print("🎵 执行高级后处理...")

            from scipy import signal  # type: ignore
            _ = signal  # 保留导入以备后用

            # 第一步: 噪音抑制
            vocals_denoised = self._spectral_subtraction_denoise(vocals, sr)

            # 第二步: 人声频率增强
            vocals_enhanced = self._vocal_frequency_enhancement(vocals_denoised, sr)

            # 第三步: 动态范围优化
            vocals_optimized = self._dynamic_range_optimization(vocals_enhanced)

            # 第四步: 最终滤波和标准化
            vocals_final = self._final_filtering_and_normalization(vocals_optimized, sr)

            return vocals_final

        except Exception as e:
            print(f"⚠️ 高级后处理失败，使用基础后处理: {e}")
            return self._basic_post_process(vocals, sr)

    def _spectral_subtraction_denoise(self, vocals, sr):
        """频谱减法降噪"""
        try:
            # 计算STFT
            S = librosa.stft(vocals, hop_length=256, n_fft=1024)
            magnitude = np.abs(S)
            phase = np.angle(S)

            # 估计噪音谱（使用前10%的帧作为噪音估计）
            noise_frames = int(magnitude.shape[1] * 0.1)
            noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)

            # 频谱减法
            alpha = 2.0  # 过减因子
            beta = 0.01  # 最小保留比例

            # 计算增益
            gain = 1 - alpha * (noise_spectrum / (magnitude + 1e-10))
            gain = np.maximum(gain, beta)  # 限制最小增益

            # 应用增益
            magnitude_denoised = magnitude * gain

            # 重构音频
            S_denoised = magnitude_denoised * np.exp(1j * phase)
            vocals_denoised = librosa.istft(S_denoised, hop_length=256, length=len(vocals))

            return vocals_denoised

        except Exception as e:
            print(f"⚠️ 频谱减法降噪失败: {e}")
            return vocals

    def _vocal_frequency_enhancement(self, vocals, sr):
        """人声频率增强"""
        try:
            from scipy import signal

            # 人声共振峰频率增强
            # 第一共振峰: 700-1100Hz (元音识别)
            # 第二共振峰: 1200-2400Hz (元音质量)
            # 第三共振峰: 2400-3200Hz (清晰度)

            nyquist = sr / 2

            # 增强第一共振峰
            f1_low, f1_high = 700 / nyquist, 1100 / nyquist
            b1, a1 = signal.butter(4, [f1_low, f1_high], btype='band')
            f1_enhanced = signal.filtfilt(b1, a1, vocals) * 1.2

            # 增强第二共振峰
            f2_low, f2_high = 1200 / nyquist, 2400 / nyquist
            b2, a2 = signal.butter(4, [f2_low, f2_high], btype='band')
            f2_enhanced = signal.filtfilt(b2, a2, vocals) * 1.3

            # 增强第三共振峰
            f3_low, f3_high = 2400 / nyquist, 3200 / nyquist
            b3, a3 = signal.butter(4, [f3_low, f3_high], btype='band')
            f3_enhanced = signal.filtfilt(b3, a3, vocals) * 1.1

            # 组合增强结果
            vocals_enhanced = vocals + 0.3 * f1_enhanced + 0.4 * f2_enhanced + 0.2 * f3_enhanced

            return vocals_enhanced

        except Exception as e:
            print(f"⚠️ 频率增强失败: {e}")
            return vocals

    def _dynamic_range_optimization(self, vocals):
        """动态范围优化"""
        try:
            # 多段压缩器
            # 轻度压缩保持自然度
            threshold = 0.3
            ratio = 3.0

            # 计算包络
            envelope = np.abs(vocals)

            # 平滑包络
            from scipy import ndimage
            envelope_smooth = ndimage.gaussian_filter1d(envelope, sigma=100)

            # 计算增益
            gain = np.ones_like(envelope_smooth)
            over_threshold = envelope_smooth > threshold

            # 对超过阈值的部分进行压缩
            gain[over_threshold] = threshold + (envelope_smooth[over_threshold] - threshold) / ratio
            gain[over_threshold] /= envelope_smooth[over_threshold]

            # 应用增益
            vocals_compressed = vocals * gain

            return vocals_compressed

        except Exception as e:
            print(f"⚠️ 动态范围优化失败: {e}")
            return vocals

    def _final_filtering_and_normalization(self, vocals, sr):
        """最终滤波和标准化"""
        try:
            from scipy import signal

            nyquist = sr / 2

            # 高通滤波器 - 去除低频噪音
            high_cutoff = 80 / nyquist
            b_high, a_high = signal.butter(6, high_cutoff, btype='high')
            vocals_filtered = signal.filtfilt(b_high, a_high, vocals)

            # 低通滤波器 - 去除高频噪音
            low_cutoff = 8000 / nyquist
            b_low, a_low = signal.butter(6, low_cutoff, btype='low')
            vocals_filtered = signal.filtfilt(b_low, a_low, vocals_filtered)

            # 去除直流分量
            vocals_filtered = vocals_filtered - np.mean(vocals_filtered)

            # 智能标准化
            # 使用RMS而不是峰值进行标准化，保持动态范围
            rms = np.sqrt(np.mean(vocals_filtered**2))
            target_rms = 0.15  # 目标RMS值

            if rms > 0:
                vocals_normalized = vocals_filtered * (target_rms / rms)
            else:
                vocals_normalized = vocals_filtered

            # 限制峰值避免削波
            max_val = np.max(np.abs(vocals_normalized))
            if max_val > 0.95:
                vocals_normalized = vocals_normalized * (0.95 / max_val)

            return vocals_normalized

        except Exception as e:
            print(f"⚠️ 最终处理失败: {e}")
            return vocals

    def _basic_post_process(self, vocals, sr):
        """基础后处理方法"""
        try:
            from scipy import signal

            # 基础滤波
            nyquist = sr / 2
            low_cutoff = 80 / nyquist
            high_cutoff = 8000 / nyquist

            b, a = signal.butter(4, [low_cutoff, high_cutoff], btype='band')
            vocals_filtered = signal.filtfilt(b, a, vocals)

            # 基础标准化
            max_val = np.max(np.abs(vocals_filtered))
            if max_val > 0:
                vocals_normalized = vocals_filtered / max_val * 0.8
            else:
                vocals_normalized = vocals_filtered

            return vocals_normalized

        except Exception as e:
            print(f"⚠️ 基础后处理失败: {e}")
            return vocals

    def _simple_vocal_extraction(self, audio_path: str) -> tuple:
        """改进的简单人声提取方法"""
        try:
            print("🔄 使用改进的简单人声提取方法...")

            # 加载音频，保持立体声
            y, sr = librosa.load(audio_path, sr=None, mono=False)

            if len(y.shape) == 2 and y.shape[0] == 2:  # 立体声
                left = y[0]
                right = y[1]

                # 使用改进的中心声道提取
                # 中心声道（人声）= (左 + 右) / 2
                center = (left + right) / 2

                # 侧声道（伴奏）= (左 - 右) / 2
                side = (left - right) / 2

                # 人声增强：中心声道减去部分侧声道
                vocals = center - 0.3 * side

                # 伴奏：原始混音减去人声
                instrumental = center + side

                # 应用简单的滤波
                vocals = self._simple_filter(vocals, sr)

            else:  # 单声道
                print("⚠️ 单声道音频，无法进行有效的人声分离")
                y_mono = librosa.to_mono(y) if len(y.shape) == 2 else y

                # 对单声道音频，只能做简单的频谱处理
                vocals = self._simple_filter(y_mono, sr)
                instrumental = y_mono - vocals * 0.5

            # 标准化音量
            vocals = self._normalize_audio(vocals)
            instrumental = self._normalize_audio(instrumental)

            # 保存分离结果
            vocals_path = os.path.join(self.temp_dir, "vocals_simple.wav")
            instrumental_path = os.path.join(self.temp_dir, "instrumental_simple.wav")

            # 使用高质量参数保存
            sf.write(vocals_path, vocals, sr, subtype='PCM_24')
            sf.write(instrumental_path, instrumental, sr, subtype='PCM_24')

            print(f"✅ 改进的简单人声提取完成: {vocals_path}")
            return vocals_path, instrumental_path

        except Exception as e:
            print(f"❌ 所有人声分离方法都失败: {e}")
            # 最后的最后，直接返回原音频
            return self._fallback_to_original(audio_path)

    def _simple_filter(self, audio, sr):
        """简单的人声频率滤波"""
        try:
            from scipy import signal

            # 人声频率范围大约在 80Hz - 8000Hz
            nyquist = sr / 2
            low_cutoff = 80 / nyquist
            high_cutoff = 8000 / nyquist

            # 带通滤波器
            b, a = signal.butter(4, [low_cutoff, high_cutoff], btype='band')
            filtered_audio = signal.filtfilt(b, a, audio)

            return filtered_audio

        except Exception:
            # 如果滤波失败，返回原音频
            return audio

    def _normalize_audio(self, audio):
        """标准化音频音量"""
        try:
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                return audio / max_val * 0.8
            else:
                return audio
        except Exception:
            return audio

    def _fallback_to_original(self, audio_path: str) -> tuple:
        """最后的备选方案：返回原音频"""
        try:
            print("🔄 所有分离方法失败，返回原音频...")

            # 直接复制原音频作为人声
            vocals_path = os.path.join(self.temp_dir, "vocals_original.wav")
            instrumental_path = os.path.join(self.temp_dir, "instrumental_empty.wav")

            # 复制原音频
            shutil.copy2(audio_path, vocals_path)

            # 创建空的伴奏文件
            y, sr = librosa.load(audio_path, sr=None)
            silence = np.zeros_like(y)
            sf.write(instrumental_path, silence, sr)

            print(f"✅ 返回原音频作为人声: {vocals_path}")
            return vocals_path, instrumental_path

        except Exception as e:
            print(f"❌ 连备选方案都失败了: {e}")
            raise

    def _simple_effective_separation(self, audio_path: str) -> tuple:
        """简单但有效的人声分离方法"""
        try:
            print("🔄 使用简单有效的人声分离方法...")

            # 加载音频
            y, sr = librosa.load(audio_path, sr=None, mono=False)

            if len(y.shape) == 2 and y.shape[0] == 2:
                # 立体声处理
                left = y[0]
                right = y[1]

                # 方法1: Karaoke效果 (中心声道消除)
                # 人声通常在中心，伴奏在两侧
                vocals_karaoke = (left + right) / 2  # 中心声道
                instrumental_karaoke = (left - right) / 2  # 侧声道

                # 方法2: 频域处理
                # 对人声进行频域增强
                vocals_enhanced = self._enhance_vocals_frequency(vocals_karaoke, sr)

                # 保存结果
                vocals_path = os.path.join(self.temp_dir, "vocals_simple.wav")
                instrumental_path = os.path.join(self.temp_dir, "instrumental_simple.wav")

                sf.write(vocals_path, vocals_enhanced, sr, subtype='PCM_24')
                sf.write(instrumental_path, instrumental_karaoke, sr, subtype='PCM_24')

                print(f"✅ 简单有效分离完成: {vocals_path}")
                return vocals_path, instrumental_path

            else:
                # 单声道处理 - 直接返回原音频
                print("⚠️ 单声道音频，无法进行有效分离，返回原音频")
                vocals_path = os.path.join(self.temp_dir, "vocals_original.wav")
                instrumental_path = os.path.join(self.temp_dir, "instrumental_empty.wav")

                # 复制原音频作为人声
                shutil.copy2(audio_path, vocals_path)

                # 创建空的伴奏
                y_mono = librosa.to_mono(y) if len(y.shape) == 2 else y
                silence = np.zeros_like(y_mono)
                sf.write(instrumental_path, silence, sr)

                return vocals_path, instrumental_path

        except Exception as e:
            print(f"❌ 简单分离方法失败: {e}")
            # 最后的最后，直接返回原音频
            return self._fallback_to_original(audio_path)

    def _enhance_vocals_frequency(self, vocals, sr):
        """增强人声频率"""
        try:
            from scipy import signal

            # 人声频率范围增强 (200Hz - 4000Hz)
            nyquist = sr / 2
            low_freq = 200 / nyquist
            high_freq = 4000 / nyquist

            # 带通滤波器
            b, a = signal.butter(4, [low_freq, high_freq], btype='band')
            vocals_filtered = signal.filtfilt(b, a, vocals)

            # 轻微增强
            vocals_enhanced = vocals * 0.7 + vocals_filtered * 0.3

            # 标准化
            max_val = np.max(np.abs(vocals_enhanced))
            if max_val > 0:
                vocals_enhanced = vocals_enhanced / max_val * 0.8

            return vocals_enhanced

        except Exception:
            # 如果增强失败，返回原音频
            return vocals

    def process_audio(self, input_path: str, output_path: str) -> bool:
        """完整的音频处理流程：30秒切割 + 人声分离"""
        try:
            print(f"🎵 开始处理音频: {input_path}")

            # 步骤1: 切割为30秒
            cut_path = os.path.join(self.temp_dir, "cut_30s.wav")
            if not self.cut_audio_30s(input_path, cut_path):
                return False

            # 验证切割后的音频
            self._verify_audio_quality(cut_path, "切割后")

            # 步骤2: 人声分离
            vocals_path, instrumental_path = self.separate_vocals(cut_path)
            _ = instrumental_path  # 保留变量以备后用

            # 验证分离后的音频质量
            self._verify_audio_quality(vocals_path, "人声分离后")

            # 步骤3: 复制到目标位置
            shutil.copy2(vocals_path, output_path)

            # 验证最终输出
            self._verify_audio_quality(output_path, "最终输出")

            print(f"✅ 音频处理完成: {output_path}")
            return True

        except Exception as e:
            print(f"❌ 音频处理失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _verify_audio_quality(self, audio_path: str, stage: str):
        """验证音频质量"""
        try:
            if not os.path.exists(audio_path):
                print(f"⚠️ {stage}: 音频文件不存在")
                return

            # 加载音频进行分析
            y, sr = librosa.load(audio_path, sr=None)

            # 计算音频统计信息
            duration = len(y) / sr
            max_amplitude = np.max(np.abs(y))
            rms_energy = np.sqrt(np.mean(y**2))
            zero_crossings = np.sum(librosa.zero_crossings(y))

            # 检查是否为静音
            is_silent = max_amplitude < 0.001

            # 检查是否削波
            is_clipped = max_amplitude > 0.99

            print(f"📊 {stage} 音频质量分析:")
            print(f"   时长: {duration:.2f}秒")
            print(f"   采样率: {sr}Hz")
            print(f"   最大振幅: {max_amplitude:.4f}")
            print(f"   RMS能量: {rms_energy:.4f}")
            print(f"   过零率: {zero_crossings}")

            if is_silent:
                print(f"   ⚠️ 警告: 音频可能为静音")
            elif is_clipped:
                print(f"   ⚠️ 警告: 音频可能被削波")
            else:
                print(f"   ✅ 音频质量正常")

        except Exception as e:
            print(f"⚠️ {stage}: 音频质量验证失败: {e}")

    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            print("✅ 临时文件清理完成")
        except Exception as e:
            print(f"⚠️ 临时文件清理失败: {e}")



def log_error_with_solution(error_msg, error_type="未知错误", solution="请检查错误信息并重试"):
    """记录错误并提供中文解决方案"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    error_solutions = {
        "CUDA": {
            "solution": "1. 检查NVIDIA驱动是否正确安装\n2. 确认CUDA版本与PyTorch兼容\n3. 重启程序或重启电脑\n4. 如果是RTX 50系显卡，请确保使用最新驱动",
            "auto_fix": "尝试切换到CPU模式"
        },
        "内存不足": {
            "solution": "1. 关闭其他占用显存的程序\n2. 降低批处理大小\n3. 使用CPU模式\n4. 重启程序释放内存",
            "auto_fix": "自动降低批处理大小"
        },
        "模型加载": {
            "solution": "1. 检查模型文件是否完整\n2. 重新下载模型文件\n3. 检查磁盘空间是否充足\n4. 确认文件路径正确",
            "auto_fix": "尝试重新加载模型"
        },
        "音频处理": {
            "solution": "1. 检查音频文件格式是否支持\n2. 确认音频文件未损坏\n3. 尝试转换音频格式\n4. 检查音频文件路径",
            "auto_fix": "尝试自动转换音频格式"
        },
        "网络连接": {
            "solution": "1. 检查网络连接\n2. 尝试使用代理\n3. 检查防火墙设置\n4. 稍后重试",
            "auto_fix": "启用离线模式"
        },
        "权限错误": {
            "solution": "1. 关闭所有打开logs.txt的程序\n2. 以管理员身份运行程序\n3. 检查文件权限设置\n4. 删除logs.txt后重新启动",
            "auto_fix": "切换到控制台输出模式"
        },
        "文件占用": {
            "solution": "1. 关闭文本编辑器和其他占用文件的程序\n2. 结束其他Python进程\n3. 重启电脑释放文件占用\n4. 检查杀毒软件是否占用文件",
            "auto_fix": "创建备用日志文件"
        }
    }

    # 根据错误类型获取解决方案
    error_info = error_solutions.get(error_type, {
        "solution": solution,
        "auto_fix": "请手动处理"
    })

    log_message = f"""
=== IndexTTS 错误报告 ===
时间: {timestamp}
错误类型: {error_type}
错误信息: {error_msg}

🔧 解决方案:
{error_info['solution']}

🤖 自动修复: {error_info['auto_fix']}

=== 错误报告结束 ===
"""

    # 尝试写入日志文件，如果失败则只输出到控制台
    try:
        with open('logs.txt', 'a', encoding='utf-8') as f:
            f.write(log_message)
    except PermissionError:
        print("⚠️ 无法写入logs.txt，权限被拒绝")
        print("💡 错误信息仅显示在控制台，请解决权限问题后重启程序")
        # 不再创建备用日志文件，避免目录混乱
    except Exception as e:
        print(f"⚠️ 日志写入失败: {e}")
        print("💡 错误信息仅显示在控制台")

    print(f"❌ 发生错误: {error_type}")
    first_solution = error_info['solution'].split('\n')[0]
    print(f"💡 建议解决方案: {first_solution}")

    return error_info

def auto_fix_common_errors(error_msg):
    """自动修复常见错误"""
    try:
        if "CUDA" in error_msg or "GPU" in error_msg:
            print("🔄 检测到GPU错误，尝试自动修复...")
            # 这里可以添加自动切换到CPU的逻辑
            return True
        elif "memory" in error_msg.lower() or "内存" in error_msg:
            print("🔄 检测到内存错误，尝试清理内存...")
            import gc
            gc.collect()
            try:
                import torch
                if hasattr(torch, 'cuda') and torch.cuda.is_available():
                    torch.cuda.empty_cache()
            except ImportError:
                pass
            return True
        elif "model" in error_msg.lower() or "模型" in error_msg:
            print("🔄 检测到模型错误，尝试重新初始化...")
            # 这里可以添加重新加载模型的逻辑
            return True
    except Exception as e:
        print(f"⚠️ 自动修复失败: {e}")

    return False

current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, "indextts"))

# 初始化日志系统
logger = setup_logging()

import argparse
parser = argparse.ArgumentParser(description="IndexTTS WebUI")
parser.add_argument("--verbose", action="store_true", default=False, help="Enable verbose mode")
parser.add_argument("--port", type=int, default=7866, help="Port to run the web UI on")
parser.add_argument("--host", type=str, default="127.0.0.1", help="Host to run the web UI on")
parser.add_argument("--model_dir", type=str, default="checkpoints", help="Model checkpoints directory")
cmd_args = parser.parse_args()

if not os.path.exists(cmd_args.model_dir):
    print(f"Model directory {cmd_args.model_dir} does not exist. Please download the model first.")
    sys.exit(1)

for file in [
    "bigvgan_generator.pth",
    "bpe.model",
    "gpt.pth",
    "config.yaml",
]:
    file_path = os.path.join(cmd_args.model_dir, file)
    if not os.path.exists(file_path):
        print(f"Required file {file_path} does not exist. Please download it.")
        sys.exit(1)

import gradio as gr

def system_check():
    """系统环境检查"""
    smart_logger.log_info("=== IndexTTS 系统检查 ===")

    # 检查Python版本
    python_version = sys.version_info
    smart_logger.log_info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")

    # 检查PyTorch和CUDA
    try:
        import torch
        smart_logger.log_info(f"PyTorch版本: {torch.__version__}")

        if torch.cuda.is_available():
            smart_logger.log_success("CUDA可用: 是")
            smart_logger.log_info(f"CUDA版本: {torch.version.cuda}")
            smart_logger.log_info(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                smart_logger.log_info(f"GPU {i}: {props.name} ({props.total_memory / 1024**3:.1f} GB)")
        else:
            smart_logger.log_warning("CUDA不可用，将使用CPU模式")
    except Exception as e:
        smart_logger.log_error(f"PyTorch检查失败: {e}")

    # 检查磁盘空间
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        smart_logger.log_info(f"磁盘空间: 总计 {total // (1024**3)} GB, 已用 {used // (1024**3)} GB, 可用 {free // (1024**3)} GB")

        if free < 5 * 1024**3:  # 少于5GB
            smart_logger.log_warning("磁盘空间不足，可能影响程序运行")
    except Exception as e:
        smart_logger.log_error(f"磁盘空间检查失败: {e}")

    smart_logger.log_info("=== 系统检查完成 ===")

def safe_init_tts():
    """安全初始化TTS模型（支持50系显卡）"""
    import os

    # 检测50系显卡并设置特殊环境
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0).lower()
            is_rtx50_series = any(x in gpu_name for x in ['rtx 50', 'rtx50', '5070', '5080', '5090'])

            if is_rtx50_series:
                smart_logger.log_info("🎮 检测到50系显卡，应用特殊优化...")
                # 设置50系显卡优化环境变量
                os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
                os.environ['TORCH_USE_CUDA_DSA'] = '1'
                os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
                # 降低内存使用
                torch.backends.cudnn.benchmark = False
                torch.backends.cudnn.deterministic = True
    except:
        pass

    try:
        smart_logger.log_process_start("初始化IndexTTS模型")
        from indextts.infer import IndexTTS
        tts = IndexTTS(model_dir=cmd_args.model_dir, cfg_path=os.path.join(cmd_args.model_dir, "config.yaml"))
        smart_logger.log_success("✅ IndexTTS模型初始化成功")
        return tts

    except RuntimeError as e:
        error_msg = str(e)

        # 特殊处理50系显卡的CUDA内核错误
        if "no kernel image is available" in error_msg:
            smart_logger.log_error("❌ 检测到50系显卡CUDA内核兼容性问题")
            smart_logger.log_info("🔧 正在尝试兼容性修复...")

            # 修复方案: 强制CPU模式
            try:
                smart_logger.log_info("🔧 切换到CPU模式...")
                os.environ['CUDA_VISIBLE_DEVICES'] = ''
                os.environ['FORCE_CPU'] = '1'

                from indextts.infer import IndexTTS
                tts = IndexTTS(model_dir=cmd_args.model_dir, cfg_path=os.path.join(cmd_args.model_dir, "config.yaml"), device="cpu")
                smart_logger.log_success("✅ CPU模式初始化成功")
                smart_logger.log_info("💡 50系显卡将使用CPU模式运行，速度较慢但稳定")
                return tts

            except Exception as e2:
                smart_logger.log_error(f"CPU模式初始化失败: {e2}")
                smart_logger.log_info("💡 建议手动执行以下命令修复:")
                smart_logger.log_info("   pip uninstall torch torchaudio -y")
                smart_logger.log_info("   pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121")
                raise e2
        else:
            smart_logger.log_error(f"模型初始化失败: {error_msg}")

            # 尝试CPU模式
            try:
                smart_logger.log_info("🔧 尝试使用CPU模式初始化...")
                from indextts.infer import IndexTTS
                tts = IndexTTS(model_dir=cmd_args.model_dir, cfg_path=os.path.join(cmd_args.model_dir, "config.yaml"), device="cpu")
                smart_logger.log_success("✅ CPU模式初始化成功")
                return tts
            except Exception as cpu_e:
                smart_logger.log_error(f"CPU模式初始化也失败: {cpu_e}")
                raise e

    except Exception as e:
        smart_logger.log_error(f"模型初始化失败: {str(e)}", e)

        # 尝试CPU模式
        try:
            smart_logger.log_info("🔧 尝试使用CPU模式初始化...")
            from indextts.infer import IndexTTS
            tts = IndexTTS(model_dir=cmd_args.model_dir, cfg_path=os.path.join(cmd_args.model_dir, "config.yaml"), device="cpu")
            smart_logger.log_success("✅ CPU模式初始化成功")
            return tts
        except Exception as cpu_e:
            smart_logger.log_error(f"CPU模式初始化也失败: {cpu_e}")

            # 提供详细的错误分析和解决方案
            smart_logger.log_info("💡 可能的解决方案:")
            smart_logger.log_info("1. 检查模型文件是否完整")
            smart_logger.log_info("2. 检查CUDA环境是否正确安装")
            smart_logger.log_info("3. 尝试重新下载模型文件")
            smart_logger.log_info("4. 检查Python环境和依赖")
            smart_logger.log_info("5. 对于50系显卡，建议使用最新版本的PyTorch")

            raise e

# 执行系统检查
system_check()

from tools.i18n.i18n import I18nAuto

i18n = I18nAuto(language="zh_CN")
MODE = 'local'

# 安全初始化TTS
try:
    tts = safe_init_tts()
except Exception as e:
    logger.error(f"无法初始化TTS模型: {e}")
    print("❌ 模型初始化失败，请检查错误日志")
    sys.exit(1)


os.makedirs("outputs/tasks",exist_ok=True)
os.makedirs("prompts",exist_ok=True)

# 设置默认参考音频为樊登音频
default_prompt_audio = os.path.join("预设样音", "男-樊登-知识付费.WAV")

with open("tests/cases.jsonl", "r", encoding="utf-8") as f:
    example_cases = []
    for line in f:
        line = line.strip()
        if not line:
            continue
        example = json.loads(line)
        # 使用樊登音频作为默认参考音频，而不是原来的sample_prompt.wav
        example_cases.append([default_prompt_audio,
                              example.get("text"), ["普通推理", "批次推理"][example.get("infer_mode", 0)]])

def gen_single_with_status(prompt, text, infer_mode, max_text_tokens_per_sentence=120, sentences_bucket_max_size=4,
                *args, progress=gr.Progress()):
    """带状态反馈的语音生成函数"""
    output_path = None
    if not output_path:
        output_path = os.path.join("outputs", f"spk_{int(time.time())}.wav")

    # 创建一个包装函数来处理进度更新，而不是直接修改 tts.gr_progress
    original_set_progress = tts._set_gr_progress
    def progress_wrapper(value, desc):
        progress(value, desc=desc)

    # 替换原始的进度更新方法
    tts._set_gr_progress = progress_wrapper

    try:
        logger.info(f"开始生成语音 - 模式: {infer_mode}, 文本长度: {len(text) if text else 0}")

        # 输入验证
        base_style = "padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: center; justify-content: center;"

        if not prompt:
            error_msg = "未提供参考音频"
            log_error_with_solution(error_msg, "输入错误", "请上传参考音频文件")
            status_html = f"<div style='{base_style} background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>❌ 生成失败：未提供参考音频</div>"
            return gr.update(value=None, visible=True), status_html

        if not text or len(text.strip()) == 0:
            error_msg = "未提供文本内容"
            log_error_with_solution(error_msg, "输入错误", "请输入要转换的文本")
            status_html = f"<div style='{base_style} background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>❌ 生成失败：未提供文本内容</div>"
            return gr.update(value=None, visible=True), status_html

        # 显示开始生成状态（暂时未使用，保留用于后续功能）
        # start_status = "<div style='padding: 8px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;'>🎙️ 正在生成语音，请稍候...</div>"

        do_sample, top_p, top_k, temperature, \
            length_penalty, num_beams, repetition_penalty, max_mel_tokens = args
        kwargs = {
            "do_sample": bool(do_sample),
            "top_p": float(top_p),
            "top_k": int(top_k) if int(top_k) > 0 else None,
            "temperature": float(temperature),
            "length_penalty": float(length_penalty),
            "num_beams": num_beams,
            "repetition_penalty": float(repetition_penalty),
            "max_mel_tokens": int(max_mel_tokens),
        }

        if infer_mode == "普通推理":
            output = tts.infer(prompt, text, output_path, verbose=cmd_args.verbose,
                            max_text_tokens_per_sentence=int(max_text_tokens_per_sentence),
                            **kwargs)
        else:
            # 批次推理
            output = tts.infer_fast(prompt, text, output_path, verbose=cmd_args.verbose,
                max_text_tokens_per_sentence=int(max_text_tokens_per_sentence),
                sentences_bucket_max_size=(sentences_bucket_max_size),
                **kwargs)

        logger.info(f"语音生成成功: {output}")

        # 计算音频时长和文件信息
        try:
            import librosa
            duration = librosa.get_duration(filename=output)
            duration_text = f"{duration:.1f}秒"

            # 获取文件信息
            file_name = os.path.basename(output)
            file_size = os.path.getsize(output)
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
        except:
            duration_text = "未知"
            file_name = os.path.basename(output) if output else "audio.wav"
            size_str = "未知"

        # 读取音频文件并转换为Base64编码
        try:
            with open(output, 'rb') as f:
                audio_content = f.read()
            audio_content_b64 = base64.b64encode(audio_content).decode('ascii')

            # 创建带下载按钮的成功状态
            success_status = f"""
            <div style='padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724; min-height: 60px; display: flex; flex-direction: column; justify-content: center;'>
                <div style='text-align: center; margin-bottom: 8px;'>
                    🎵 语音生成成功！
                </div>
                <div style='font-size: 12px; text-align: center; margin-bottom: 8px;'>
                    📄 文件名: {file_name}<br>
                    ⏱️ 时长: {duration_text} | 📊 大小: {size_str}
                </div>
                <div style='text-align: center; font-size: 12px;'>
                    <a href="data:audio/wav;base64,{audio_content_b64}" download="{file_name}" style='color: #155724; text-decoration: none; font-weight: bold; padding: 8px 16px; background-color: rgba(21, 87, 36, 0.1); border-radius: 6px; border: 1px solid #155724; display: inline-block; transition: all 0.3s ease;' onmouseover='this.style.backgroundColor="rgba(21, 87, 36, 0.2)"; this.style.transform="translateY(-1px)";' onmouseout='this.style.backgroundColor="rgba(21, 87, 36, 0.1)"; this.style.transform="translateY(0)";'>
                        🎧 点击此处下载音频文件
                    </a>
                </div>
            </div>
            """
        except Exception as e:
            # 如果读取文件失败，显示简化版本
            success_status = f"<div style='{base_style} background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'>✅ 语音生成成功！时长: {duration_text}</div>"

        return gr.update(value=output, visible=True), success_status

    except Exception as e:
        error_msg = str(e)
        error_traceback = traceback.format_exc()

        # 分类错误类型
        if "CUDA" in error_msg or "GPU" in error_msg:
            error_type = "CUDA"
            user_error = "GPU相关错误，请检查显卡驱动"
        elif "memory" in error_msg.lower() or "内存" in error_msg:
            error_type = "内存不足"
            user_error = "内存不足，请关闭其他程序或降低参数"
        elif "model" in error_msg.lower() or "模型" in error_msg:
            error_type = "模型加载"
            user_error = "模型加载错误，请检查模型文件"
        elif "audio" in error_msg.lower() or "音频" in error_msg:
            error_type = "音频处理"
            user_error = "音频处理错误，请检查音频文件格式"
        else:
            error_type = "未知错误"
            user_error = "生成过程中出现错误"

        # 记录详细错误信息
        detailed_error = f"{error_msg}\n\n详细堆栈:\n{error_traceback}"
        log_error_with_solution(detailed_error, error_type)

        # 尝试自动修复
        if auto_fix_common_errors(error_msg):
            logger.info("尝试自动修复后重新生成...")
            try:
                # 重试一次
                if infer_mode == "普通推理":
                    output = tts.infer(prompt, text, output_path, verbose=cmd_args.verbose,
                                    max_text_tokens_per_sentence=int(max_text_tokens_per_sentence),
                                    **kwargs)
                else:
                    output = tts.infer_fast(prompt, text, output_path, verbose=cmd_args.verbose,
                        max_text_tokens_per_sentence=int(max_text_tokens_per_sentence),
                        sentences_bucket_max_size=(sentences_bucket_max_size),
                        **kwargs)
                logger.info("自动修复成功，语音生成完成")

                # 计算音频时长和文件信息
                try:
                    import librosa
                    duration = librosa.get_duration(filename=output)
                    duration_text = f"{duration:.1f}秒"

                    # 获取文件信息
                    file_name = os.path.basename(output)
                    file_size = os.path.getsize(output)
                    if file_size < 1024:
                        size_str = f"{file_size} B"
                    elif file_size < 1024 * 1024:
                        size_str = f"{file_size / 1024:.1f} KB"
                    else:
                        size_str = f"{file_size / (1024 * 1024):.1f} MB"
                except:
                    duration_text = "未知"
                    file_name = os.path.basename(output) if output else "audio.wav"
                    size_str = "未知"

                # 读取音频文件并转换为Base64编码
                try:
                    with open(output, 'rb') as f:
                        audio_content = f.read()
                    audio_content_b64 = base64.b64encode(audio_content).decode('ascii')

                    # 创建带下载按钮的成功状态
                    success_status = f"""
                    <div style='padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724; min-height: 60px; display: flex; flex-direction: column; justify-content: center;'>
                        <div style='text-align: center; margin-bottom: 8px;'>
                            🔧 自动修复成功，语音生成完成！
                        </div>
                        <div style='font-size: 12px; text-align: center; margin-bottom: 8px;'>
                            📄 文件名: {file_name}<br>
                            ⏱️ 时长: {duration_text} | 📊 大小: {size_str}
                        </div>
                        <div style='text-align: center; font-size: 12px;'>
                            <a href="data:audio/wav;base64,{audio_content_b64}" download="{file_name}" style='color: #155724; text-decoration: none; font-weight: bold; padding: 8px 16px; background-color: rgba(21, 87, 36, 0.1); border-radius: 6px; border: 1px solid #155724; display: inline-block; transition: all 0.3s ease;' onmouseover='this.style.backgroundColor="rgba(21, 87, 36, 0.2)"; this.style.transform="translateY(-1px)";' onmouseout='this.style.backgroundColor="rgba(21, 87, 36, 0.1)"; this.style.transform="translateY(0)";'>
                                🎧 点击此处下载音频文件
                            </a>
                        </div>
                    </div>
                    """
                except Exception as e:
                    # 如果读取文件失败，显示简化版本
                    success_status = f"<div style='{base_style} background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'>✅ 自动修复成功，语音生成完成！时长: {duration_text}</div>"

                return gr.update(value=output, visible=True), success_status
            except Exception as retry_e:
                logger.error(f"自动修复失败: {retry_e}")

        # 返回错误信息给用户
        error_status = f"<div style='{base_style} background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>❌ {user_error}</div>"
        return gr.update(value=None, visible=True), error_status

    finally:
        # 恢复原始的进度更新方法
        tts._set_gr_progress = original_set_progress

def gen_single(prompt, text, infer_mode, max_text_tokens_per_sentence=120, sentences_bucket_max_size=4,
                *args, progress=gr.Progress()):
    """保持原有接口兼容性的包装函数"""
    result, _ = gen_single_with_status(prompt, text, infer_mode, max_text_tokens_per_sentence, sentences_bucket_max_size, *args, progress=progress)
    return result

def update_prompt_audio():
    update_button = gr.update(interactive=True)
    return update_button

with gr.Blocks(title="IndexTTS Demo") as demo:
    mutex = threading.Lock()
    gr.HTML('''
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        padding: 20px;
        border-radius: 10px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 20px;
        font-size: 24px;
    ">
        Index-TTS（一款工业级可控且高效的零样本文本转语音系统）
    </div>
    ''')
    with gr.Tab("音频生成"):
        with gr.Row():
            # 第一列：参考音频和预设样音
            with gr.Column(scale=1):
                # 参考音频区域
                prompt_audio = gr.Audio(
                    label="🎵 参考音频",
                    sources=["upload","microphone"],
                    type="filepath",
                    interactive=True
                )

                # 预设样音选择区域
                display_names, file_mapping = get_preset_audio_list()
                preset_dropdown = gr.Dropdown(
                    choices=["请选择预设样音"] + display_names,
                    value="请选择预设样音",
                    label="选择预设样音",
                    filterable=True,  # 支持搜索
                    allow_custom_value=False,
                    info="支持搜索快速定位样音"
                )

                # 样音管理按钮（水平排列）
                with gr.Row():
                    upload_audio_btn = gr.UploadButton(
                        "📁 上传样音",
                        file_types=["audio"],
                        file_count="single"
                    )
                    delete_audio_btn = gr.Button("🗑️ 删除样音", variant="secondary")

                    # 删除确认对话框
                    with gr.Row(visible=False) as delete_confirm_row:
                        with gr.Column():
                            delete_confirm_msg = gr.HTML(
                                value="<div style='padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; text-align: center;'>⚠️ 确认要删除选中的样音吗？此操作不可撤销！</div>"
                            )
                            with gr.Row():
                                confirm_delete_btn = gr.Button("✅ 确认删除", variant="stop", size="sm")
                                cancel_delete_btn = gr.Button("❌ 取消", variant="secondary", size="sm")

            # 第二列：文本输入和控制
            with gr.Column(scale=1):
                input_text_single = gr.TextArea(
                    label="📝 文本",
                    key="input_text_single",
                    placeholder="请输入目标文本",
                    info="当前模型版本{}".format(tts.model_version or "1.0"),
                    lines=6
                )

                # 推理模式选择（水平排列）
                infer_mode = gr.Radio(
                    choices=["普通推理", "批次推理"],
                    label="⚙️ 推理模式",
                    info="批次推理：更适合长句，性能翻倍",
                    value="普通推理"
                )

                gen_button = gr.Button("🎙️ 生成语音", key="gen_button", interactive=True, variant="primary", size="lg")

            # 第三列：生成结果和状态反馈
            with gr.Column(scale=1):
                # 生成结果区域
                output_audio = gr.Audio(label="🎵 生成结果", visible=True, key="output_audio")

                # 字幕功能区域
                with gr.Row():
                    upload_subtitle_btn = gr.UploadButton(
                        "📄 上传字幕",
                        file_types=[".srt", ".vtt", ".txt"],
                        file_count="single",
                        variant="secondary"
                    )
                    generate_subtitle_btn = gr.Button("💾 生成字幕", variant="secondary", size="lg")



                # 清除缓存按钮
                clear_cache_btn = gr.Button("🗑️ 清除音频缓存", variant="secondary", size="lg")

                # 清除缓存确认对话框
                with gr.Row(visible=False) as clear_cache_confirm_row:
                    with gr.Column():
                        clear_cache_confirm_msg = gr.HTML(
                            value="<div style='padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; text-align: center;'>⚠️ 确认要清除所有音频缓存吗？这将删除outputs文件夹中的所有音频和字幕文件！</div>"
                        )
                        with gr.Row():
                            confirm_clear_cache_btn = gr.Button("✅ 确认清除", variant="stop", size="sm")
                            cancel_clear_cache_btn = gr.Button("❌ 取消", variant="secondary", size="sm")

                # 状态反馈区域
                status_display = gr.HTML(
                    label="📊 状态信息",
                    value="<div style='padding: 10px; background-color: #f0f0f0; border-radius: 5px; text-align: center; min-height: 60px; display: flex; align-items: center; justify-content: center;'>💡 准备就绪，请选择样音和输入文本</div>"
                )
        with gr.Accordion("高级生成参数设置", open=False):
            with gr.Row():
                with gr.Column(scale=1):
                    gr.Markdown("**GPT2 采样设置** _参数会影响音频多样性和生成速度详见[Generation strategies](https://huggingface.co/docs/transformers/main/en/generation_strategies)_")
                    with gr.Row():
                        do_sample = gr.Checkbox(label="do_sample", value=True, info="是否进行采样")
                        temperature = gr.Slider(label="temperature", minimum=0.1, maximum=2.0, value=1.0, step=0.1)
                    with gr.Row():
                        top_p = gr.Slider(label="top_p", minimum=0.0, maximum=1.0, value=0.8, step=0.01)
                        top_k = gr.Slider(label="top_k", minimum=0, maximum=100, value=30, step=1)
                        num_beams = gr.Slider(label="num_beams", value=3, minimum=1, maximum=10, step=1)
                    with gr.Row():
                        repetition_penalty = gr.Number(label="repetition_penalty", precision=None, value=10.0, minimum=0.1, maximum=20.0, step=0.1)
                        length_penalty = gr.Number(label="length_penalty", precision=None, value=0.0, minimum=-2.0, maximum=2.0, step=0.1)
                    max_mel_tokens = gr.Slider(label="max_mel_tokens", value=600, minimum=50, maximum=tts.cfg.gpt.max_mel_tokens, step=10, info="生成Token最大数量，过小导致音频被截断", key="max_mel_tokens")
                    # with gr.Row():
                    #     typical_sampling = gr.Checkbox(label="typical_sampling", value=False, info="不建议使用")
                    #     typical_mass = gr.Slider(label="typical_mass", value=0.9, minimum=0.0, maximum=1.0, step=0.1)
                with gr.Column(scale=2):
                    gr.Markdown("**分句设置** _参数会影响音频质量和生成速度_")
                    with gr.Row():
                        max_text_tokens_per_sentence = gr.Slider(
                            label="分句最大Token数", value=120, minimum=20, maximum=tts.cfg.gpt.max_text_tokens, step=2, key="max_text_tokens_per_sentence",
                            info="建议80~200之间，值越大，分句越长；值越小，分句越碎；过小过大都可能导致音频质量不高",
                        )
                        sentences_bucket_max_size = gr.Slider(
                            label="分句分桶的最大容量（批次推理生效）", value=4, minimum=1, maximum=16, step=1, key="sentences_bucket_max_size",
                            info="建议2-8之间，值越大，一批次推理包含的分句数越多，过大可能导致内存溢出",
                        )
                    with gr.Accordion("预览分句结果", open=True) as sentences_settings:
                        sentences_preview = gr.Dataframe(
                            headers=["序号", "分句内容", "Token数"],
                            key="sentences_preview",
                            wrap=True,
                        )
            advanced_params = [
                do_sample, top_p, top_k, temperature,
                length_penalty, num_beams, repetition_penalty, max_mel_tokens,
                # typical_sampling, typical_mass,
            ]
        
        # 添加Examples点击事件处理函数
        def on_example_select_handler(prompt_audio_path, text, mode):
            """当选择Examples时，自动选择对应的预设样音"""
            # 获取当前预设样音列表
            display_names, file_mapping = get_preset_audio_list()

            # 查找对应的预设样音
            selected_preset = "请选择预设样音"  # 默认值

            if prompt_audio_path:
                # 提取文件名
                audio_filename = os.path.basename(prompt_audio_path)

                # 在file_mapping中查找对应的显示名称
                for display_name, full_filename in file_mapping.items():
                    if full_filename == audio_filename:
                        selected_preset = display_name
                        break

            smart_logger.log_info(f"📝 Examples选择: {prompt_audio_path} -> 预设样音: {selected_preset}")
            return gr.update(value=selected_preset)

        if len(example_cases) > 0:
            examples_component = gr.Examples(
                examples=example_cases,
                inputs=[prompt_audio, input_text_single, infer_mode],
                outputs=[preset_dropdown],
                fn=on_example_select_handler,
                cache_examples=False,
            )

    def on_input_text_change(text, max_tokens_per_sentence):
        if text and len(text) > 0:
            text_tokens_list = tts.tokenizer.tokenize(text)

            sentences = tts.tokenizer.split_sentences(text_tokens_list, max_tokens_per_sentence=int(max_tokens_per_sentence))
            data = []
            for i, s in enumerate(sentences):
                sentence_str = ''.join(s)
                tokens_count = len(s)
                data.append([i, sentence_str, tokens_count])

            return {
                sentences_preview: gr.update(value=data, visible=True, type="array"),
            }
        else:
            # 使用已导入的 pandas 创建空数据框
            empty_df = pd.DataFrame([], columns=["序号", "分句内容", "Token数"])
            return {
                sentences_preview: gr.update(value=empty_df)
            }

    def on_preset_select(selected_display_name):
        """当选择预设样音时的处理函数"""
        if selected_display_name and selected_display_name != "请选择预设样音":
            display_names, file_mapping = get_preset_audio_list()
            _ = display_names  # 保留变量以备后用
            if selected_display_name in file_mapping:
                full_file_name = file_mapping[selected_display_name]
                preset_path = os.path.join("预设样音", full_file_name)
                if os.path.exists(preset_path):
                    status_msg = f"<div style='padding: 10px; background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; color: #155724; min-height: 60px; display: flex; align-items: center; justify-content: center;'>✅ 已选择样音: {selected_display_name}</div>"
                    return preset_path, status_msg
                else:
                    error_msg = f"<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 文件不存在: {selected_display_name}</div>"
                    return None, error_msg
            else:
                error_msg = f"<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 文件映射不存在: {selected_display_name}</div>"
                return None, error_msg
        return None, "<div style='padding: 10px; background-color: #f0f0f0; border-radius: 4px; text-align: center; min-height: 60px; display: flex; align-items: center; justify-content: center;'>💡 请选择预设样音</div>"

    def on_upload_audio(uploaded_file):
        """处理音频上传"""
        if uploaded_file is None:
            error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 请选择要上传的音频文件</div>"
            return gr.update(), error_msg, gr.update()

        selected_audio, status_msg, new_preset_list = upload_preset_audio(uploaded_file)

        # 格式化状态消息
        base_style = "padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: center; justify-content: center;"
        if "✅" in status_msg:
            formatted_status = f"<div style='{base_style} background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'>{status_msg}</div>"
        else:
            formatted_status = f"<div style='{base_style} background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>{status_msg}</div>"

        # 确保上传后显示默认选项
        choices_with_default = ["请选择预设样音"] + new_preset_list

        # 如果上传成功，自动选择新上传的音频
        if selected_audio:
            display_names, file_mapping = get_preset_audio_list()
            _ = display_names  # 保留变量以备后用
            if selected_audio in file_mapping:
                preset_path = os.path.join("预设样音", file_mapping[selected_audio])
                return (
                    gr.update(choices=choices_with_default, value=selected_audio),
                    formatted_status,
                    gr.update(value=preset_path)
                )

        return (
            gr.update(choices=choices_with_default, value="请选择预设样音"),
            formatted_status,
            gr.update()
        )

    def on_delete_audio_click(selected_audio):
        """点击删除按钮时显示确认对话框"""
        if not selected_audio or selected_audio == "请选择预设样音":
            error_msg = "⚠️ 请先选择要删除的样音"
            formatted_status = f"<div style='padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: center; justify-content: center; background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>{error_msg}</div>"
            return (
                formatted_status,
                gr.update(visible=False),  # 隐藏确认对话框
                gr.update(value=f"<div style='padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; text-align: center;'>⚠️ 确认要删除样音 <strong>{selected_audio}</strong> 吗？此操作不可撤销！</div>")
            )

        # 显示确认对话框
        confirm_msg = f"<div style='padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; text-align: center;'>⚠️ 确认要删除样音 <strong>{selected_audio}</strong> 吗？此操作不可撤销！</div>"
        return (
            gr.update(),  # 状态显示不变
            gr.update(visible=True),  # 显示确认对话框
            gr.update(value=confirm_msg)  # 更新确认消息
        )

    def on_confirm_delete(selected_audio):
        """确认删除音频"""
        status_msg, new_preset_list, remaining_selection = delete_preset_audio(selected_audio)

        # 格式化状态消息 - 增强删除反馈的视觉效果
        if "🗑️" in status_msg and "成功" in status_msg:
            # 删除成功的特殊格式
            lines = status_msg.split('\n')
            formatted_status = f"""
            <div style='padding: 15px; background-color: #d4edda; border: 2px solid #c3e6cb; border-radius: 8px; color: #155724; min-height: 80px; display: flex; flex-direction: column; justify-content: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                <div style='text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 8px;'>
                    {lines[0] if lines else status_msg}
                </div>
                <div style='font-size: 12px; text-align: center; line-height: 1.4;'>
                    {('<br>'.join(lines[1:]) if len(lines) > 1 else '')}
                </div>
            </div>
            """
        elif "✅" in status_msg or "成功" in status_msg:
            # 其他成功消息
            formatted_status = f"<div style='padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: center; justify-content: center; background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724;'>{status_msg}</div>"
        else:
            # 错误消息
            formatted_status = f"<div style='padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: center; justify-content: center; background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>{status_msg}</div>"

        # 确保删除后显示默认选项
        choices_with_default = ["请选择预设样音"] + new_preset_list
        default_value = remaining_selection if remaining_selection else "请选择预设样音"

        return (
            gr.update(choices=choices_with_default, value=default_value),
            formatted_status,
            gr.update(value=None),
            gr.update(visible=False)  # 隐藏确认对话框
        )

    def on_cancel_delete():
        """取消删除操作"""
        return (
            gr.update(),  # 状态显示不变
            gr.update(visible=False)  # 隐藏确认对话框
        )

    def on_clear_cache_click():
        """点击清除缓存按钮时显示确认对话框"""
        # 显示确认对话框
        confirm_msg = "<div style='padding: 10px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404; text-align: center;'>⚠️ 确认要清除所有音频缓存吗？<br>这将删除outputs文件夹中的所有音频和字幕文件！<br><strong>此操作不可撤销！</strong></div>"
        return (
            gr.update(),  # 状态显示不变
            gr.update(visible=True),  # 显示确认对话框
            gr.update(value=confirm_msg)  # 更新确认消息
        )

    def on_confirm_clear_cache():
        """确认清除音频缓存"""
        status_msg = clear_audio_cache()

        # 格式化状态消息，调整高度和对齐 - 增强清除缓存反馈的视觉效果
        if "✅" in status_msg and "清理完成" in status_msg:
            # 清除成功的特殊格式
            formatted_status = f"""
            <div style='padding: 15px; background-color: #d4edda; border: 2px solid #c3e6cb; border-radius: 8px; color: #155724; min-height: 100px; display: flex; flex-direction: column; justify-content: flex-start; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                <div style='text-align: center; font-size: 16px; font-weight: bold; margin-bottom: 10px;'>
                    🗑️ 缓存清理完成！
                </div>
                <div style='font-size: 12px; line-height: 1.6; white-space: pre-line;'>
                    {status_msg.replace('✅ 缓存清理完成！', '').strip()}
                </div>
            </div>
            """
        elif "💡" in status_msg:
            formatted_status = f"<div style='padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: flex-start; justify-content: flex-start; flex-direction: column; background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460;'>{status_msg}</div>"
        else:
            formatted_status = f"<div style='padding: 10px; border-radius: 4px; min-height: 60px; display: flex; align-items: flex-start; justify-content: flex-start; flex-direction: column; background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;'>{status_msg}</div>"

        return (
            formatted_status,
            gr.update(visible=False)  # 隐藏确认对话框
        )

    def on_cancel_clear_cache():
        """取消清除缓存操作"""
        return (
            gr.update(),  # 状态显示不变
            gr.update(visible=False)  # 隐藏确认对话框
        )

    def on_upload_subtitle(uploaded_file):
        """处理字幕文件上传"""
        if not uploaded_file:
            error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 请选择要上传的字幕文件</div>"
            return "", error_msg

        try:
            # 解析字幕文件
            extracted_text = parse_subtitle_file(uploaded_file)

            if extracted_text:
                success_msg = f"<div style='padding: 10px; background-color: #e1bee7; border: 1px solid #ce93d8; border-radius: 4px; color: #4a148c; min-height: 60px; display: flex; align-items: center; justify-content: center;'>📄 字幕上传成功，已提取文本内容</div>"
                return extracted_text, success_msg
            else:
                error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 字幕文件格式不支持或解析失败</div>"
                return "", error_msg

        except Exception as e:
            error_msg = f"<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 字幕处理失败: {str(e)}</div>"
            return "", error_msg

    def on_generate_subtitle(audio_file, text_content):
        """生成字幕文件"""
        if not audio_file:
            error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 请先生成音频</div>"
            return error_msg

        if not text_content or not text_content.strip():
            error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 请先输入文本内容</div>"
            return error_msg

        try:
            # 生成字幕文件
            subtitle_path = generate_subtitle_from_audio(audio_file, text_content)

            if subtitle_path and os.path.exists(subtitle_path):
                # 获取文件名用于显示
                file_name = os.path.basename(subtitle_path)
                file_size = os.path.getsize(subtitle_path)

                # 格式化文件大小
                if file_size < 1024:
                    size_str = f"{file_size} B"
                elif file_size < 1024 * 1024:
                    size_str = f"{file_size / 1024:.1f} KB"
                else:
                    size_str = f"{file_size / (1024 * 1024):.1f} MB"

                # 读取字幕文件内容并进行Base64编码
                import base64
                with open(subtitle_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # 将文件内容转换为Base64编码，避免特殊字符问题
                file_content_b64 = base64.b64encode(file_content.encode('utf-8')).decode('ascii')

                success_msg = f"""
                <div style='padding: 10px; background-color: #e1bee7; border: 1px solid #ce93d8; border-radius: 4px; color: #4a148c; min-height: 60px; display: flex; flex-direction: column; justify-content: center;'>
                    <div style='text-align: center; margin-bottom: 8px;'>
                        💾 字幕生成成功！
                    </div>
                    <div style='font-size: 12px; text-align: center; margin-bottom: 8px;'>
                        📄 文件名: {file_name}<br>
                        📊 文件大小: {size_str}
                    </div>
                    <div style='text-align: center; font-size: 12px;'>
                        <a href="data:text/plain;charset=utf-8;base64,{file_content_b64}" download="{file_name}" style='color: #6a1b9a; text-decoration: none; font-weight: bold; padding: 8px 16px; background-color: rgba(106, 27, 154, 0.1); border-radius: 6px; border: 1px solid #6a1b9a; display: inline-block; transition: all 0.3s ease;' onmouseover='this.style.backgroundColor="rgba(106, 27, 154, 0.2)"; this.style.transform="translateY(-1px)";' onmouseout='this.style.backgroundColor="rgba(106, 27, 154, 0.1)"; this.style.transform="translateY(0)";'>
                            ⬇️ 点击此处下载字幕文件
                        </a>
                    </div>
                </div>
                """
                return success_msg
            else:
                error_msg = "<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 字幕生成失败</div>"
                return error_msg

        except Exception as e:
            error_msg = f"<div style='padding: 10px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; color: #721c24; min-height: 60px; display: flex; align-items: center; justify-content: center;'>❌ 字幕生成失败: {str(e)}</div>"
            return error_msg

    # 预设样音相关事件绑定
    preset_dropdown.change(
        on_preset_select,
        inputs=[preset_dropdown],
        outputs=[prompt_audio, status_display]
    )

    upload_audio_btn.upload(
        on_upload_audio,
        inputs=[upload_audio_btn],
        outputs=[preset_dropdown, status_display, prompt_audio]
    )

    # 删除按钮点击事件 - 显示确认对话框
    delete_audio_btn.click(
        on_delete_audio_click,
        inputs=[preset_dropdown],
        outputs=[status_display, delete_confirm_row, delete_confirm_msg]
    )

    # 确认删除按钮事件
    confirm_delete_btn.click(
        on_confirm_delete,
        inputs=[preset_dropdown],
        outputs=[preset_dropdown, status_display, prompt_audio, delete_confirm_row]
    )

    # 取消删除按钮事件
    cancel_delete_btn.click(
        on_cancel_delete,
        outputs=[status_display, delete_confirm_row]
    )

    # 字幕功能事件绑定
    upload_subtitle_btn.upload(
        on_upload_subtitle,
        inputs=[upload_subtitle_btn],
        outputs=[input_text_single, status_display]
    )

    generate_subtitle_btn.click(
        on_generate_subtitle,
        inputs=[output_audio, input_text_single],
        outputs=[status_display]
    )

    # 清除缓存按钮点击事件 - 显示确认对话框
    clear_cache_btn.click(
        on_clear_cache_click,
        outputs=[status_display, clear_cache_confirm_row, clear_cache_confirm_msg]
    )

    # 确认清除缓存按钮事件
    confirm_clear_cache_btn.click(
        on_confirm_clear_cache,
        outputs=[status_display, clear_cache_confirm_row]
    )

    # 取消清除缓存按钮事件
    cancel_clear_cache_btn.click(
        on_cancel_clear_cache,
        outputs=[status_display, clear_cache_confirm_row]
    )



    # 原有事件绑定
    input_text_single.change(
        on_input_text_change,
        inputs=[input_text_single, max_text_tokens_per_sentence],
        outputs=[sentences_preview]
    )
    max_text_tokens_per_sentence.change(
        on_input_text_change,
        inputs=[input_text_single, max_text_tokens_per_sentence],
        outputs=[sentences_preview]
    )
    prompt_audio.upload(update_prompt_audio,
                         inputs=[],
                         outputs=[gen_button])

    gen_button.click(gen_single_with_status,
                     inputs=[prompt_audio, input_text_single, infer_mode,
                             max_text_tokens_per_sentence, sentences_bucket_max_size,
                             *advanced_params,
                     ],
                     outputs=[output_audio, status_display])


def find_available_port(start_port: int = 7866, max_attempts: int = 10) -> int:
    """查找可用端口"""
    import socket

    for i in range(max_attempts):
        port = start_port + i
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                smart_logger.log_info(f"🔍 找到可用端口: {port}")
                return port
        except OSError:
            smart_logger.log_info(f"⚠️ 端口 {port} 已被占用，尝试下一个...")
            continue

    # 如果都被占用，返回一个随机端口
    import random
    random_port = random.randint(8000, 9000)
    smart_logger.log_warning(f"使用随机端口: {random_port}")
    return random_port

if __name__ == "__main__":
    try:
        demo.queue(20)

        # 自动查找可用端口
        if cmd_args.port == 7866:  # 如果使用默认端口，则自动查找
            available_port = find_available_port(cmd_args.port)
            if available_port != cmd_args.port:
                smart_logger.log_info(f"🔄 端口 {cmd_args.port} 被占用，自动切换到端口 {available_port}")
                cmd_args.port = available_port

        smart_logger.log_info("🚀 IndexTTS WebUI 启动完成")
        smart_logger.log_info(f"🌐 访问地址: http://{cmd_args.host}:{cmd_args.port}")
        demo.launch(server_name=cmd_args.host, server_port=cmd_args.port, share=False, inbrowser=True)
    except KeyboardInterrupt:
        smart_logger.log_info("🛑 用户中断程序")
    except Exception as e:
        smart_logger.log_error(f"程序运行错误: {e}")
        # 如果还是端口问题，尝试使用随机端口
        if "port" in str(e).lower():
            try:
                random_port = find_available_port(8000, 5)
                smart_logger.log_info(f"🔄 尝试使用备用端口: {random_port}")
                demo.launch(server_name=cmd_args.host, server_port=random_port, share=False, inbrowser=True)
            except Exception as e2:
                smart_logger.log_error(f"备用端口启动也失败: {e2}")
    finally:
        # 停止日志监控
        log_analyzer.stop_monitoring()
        smart_logger.log_info("👋 IndexTTS 已安全退出")
