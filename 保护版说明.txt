# IndexTTS WebUI 受保护版本使用说明

## 文件说明
- `webui_protected.py`: 受保护的主程序文件
- `启动IndexTTS.bat`: 一键启动脚本
- `保护版说明.txt`: 本说明文件
- `预设样音/`: 预设音频文件夹

## 使用方法
1. 双击 `启动IndexTTS.bat` 启动程序
2. 或者在命令行运行: `python webui_protected.py`
3. 程序会自动打开浏览器访问WebUI界面

## 保护特性
- ✅ 代码已编译为字节码，无法直接查看源码
- ✅ 字节码经过压缩和Base64编码
- ✅ 变量名随机化，增加逆向难度
- ✅ 内置完整性检查，修改后无法运行
- ✅ 保留完整功能，用户体验不变

## 安全说明
- 此版本有效防止代码被轻易查看和修改
- 任何对程序文件的修改都会导致程序无法运行
- 提供基础的反盗版保护
- 适合一般用户的保护需求

## 注意事项
- 请勿修改 `webui_protected.py` 文件
- 确保Python环境正常安装
- 如遇问题请查看控制台错误信息
- 建议保留原始备份文件

## 分发说明
分发时请包含以下文件：
- webui_protected.py (必需)
- 启动IndexTTS.bat (推荐)
- 预设样音/ (如果需要)
- 保护版说明.txt (推荐)

## 故障排除
如果程序无法启动：
1. 检查Python是否正确安装
2. 确保所有依赖库已安装
3. 以管理员身份运行
4. 查看详细错误信息
