#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的WebUI打包工具
使用基础PyInstaller打包，避免复杂的配置问题
"""

import os
import sys
import subprocess
import shutil

def simple_pack():
    """简单打包webui.py"""
    print("=" * 60)
    print("🚀 IndexTTS WebUI 简化打包工具")
    print("=" * 60)
    
    # 检查源文件
    source_file = "webui.py"
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    print(f"✅ 源文件存在: {source_file}")
    
    # 检查PyInstaller
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ PyInstaller: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装，正在安装...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 清理之前的构建
    print("🧹 清理之前的构建文件...")
    dirs_to_clean = ["dist", "build", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"   删除文件: {file_path}")
    
    # 使用最简单的PyInstaller命令
    print("📦 开始打包...")
    print("💡 使用简化配置，避免复杂依赖问题")
    
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个文件
        '--console',  # 保留控制台
        '--name', 'IndexTTS-WebUI',  # 指定输出文件名
        '--add-data', '预设样音;预设样音',  # 添加预设样音文件夹
        '--hidden-import', 'gradio',
        '--hidden-import', 'torch',
        '--hidden-import', 'librosa',
        '--hidden-import', 'soundfile',
        '--hidden-import', 'numpy',
        '--hidden-import', 'pandas',
        '--collect-all', 'gradio',  # 收集gradio的所有文件
        source_file
    ]
    
    print(f"   执行命令: {' '.join(cmd)}")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 打包成功")
        else:
            print("❌ 打包失败")
            print("错误信息:")
            print(result.stderr)
            
            # 尝试更简单的命令
            print("\n🔄 尝试更简单的打包方式...")
            simple_cmd = [
                'pyinstaller',
                '--onefile',
                '--console',
                '--name', 'IndexTTS-WebUI',
                source_file
            ]
            
            print(f"   执行命令: {' '.join(simple_cmd)}")
            result2 = subprocess.run(simple_cmd, capture_output=True, text=True, timeout=600)
            
            if result2.returncode == 0:
                print("✅ 简化打包成功")
            else:
                print("❌ 简化打包也失败")
                print("错误信息:")
                print(result2.stderr)
                return False
    
    except subprocess.TimeoutExpired:
        print("❌ 打包超时")
        return False
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False
    
    # 验证输出
    exe_path = os.path.join("dist", "IndexTTS-WebUI.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path)
        size_mb = file_size / (1024 * 1024)
        print(f"✅ 可执行文件已生成: {exe_path}")
        print(f"   文件大小: {size_mb:.1f} MB")
        
        # 复制预设样音文件夹到dist目录
        if os.path.exists("预设样音"):
            try:
                shutil.copytree("预设样音", "dist/预设样音", dirs_exist_ok=True)
                print("✅ 预设样音文件夹已复制到输出目录")
            except Exception as e:
                print(f"⚠️ 复制预设样音失败: {e}")
        
        # 创建使用说明
        create_simple_readme()
        
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print("=" * 60)
        print("📁 输出目录: dist/")
        print("📦 程序文件: dist/IndexTTS-WebUI.exe")
        print("📁 预设样音: dist/预设样音/")
        print("📖 使用说明: dist/README.txt")
        print("\n💡 提示:")
        print("   - 程序已打包为单个exe文件")
        print("   - 代码已编译，难以直接修改")
        print("   - 可以安全分发给用户使用")
        print("   - 建议测试运行确保功能正常")
        
        return True
    else:
        print(f"❌ 可执行文件未找到: {exe_path}")
        return False

def create_simple_readme():
    """创建简单的使用说明"""
    readme_content = """# IndexTTS WebUI 打包版使用说明

## 文件说明
- `IndexTTS-WebUI.exe`: 打包后的主程序
- `预设样音/`: 预设音频文件夹
- `README.txt`: 本说明文件

## 使用方法
1. 双击 `IndexTTS-WebUI.exe` 启动程序
2. 程序会自动打开浏览器访问WebUI界面
3. 如果浏览器没有自动打开，请手动访问控制台显示的地址

## 注意事项
- 首次运行可能需要较长时间加载
- 请确保系统已安装必要的运行库
- 如遇到问题，请查看控制台输出的错误信息
- 请将exe文件和预设样音文件夹放在同一目录下

## 安全特性
- 代码已编译为字节码，难以直接修改
- PyInstaller打包提供基础保护
- 防止简单的代码查看和修改

## 故障排除
如果程序无法启动，请尝试：
1. 以管理员身份运行
2. 检查防病毒软件是否误报
3. 确保系统已安装Visual C++ Redistributable
4. 查看控制台错误信息
"""
    
    os.makedirs("dist", exist_ok=True)
    with open("dist/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 使用说明已创建: dist/README.txt")

if __name__ == "__main__":
    success = simple_pack()
    
    if success:
        print("\n✅ 打包成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 打包失败！")
        sys.exit(1)
