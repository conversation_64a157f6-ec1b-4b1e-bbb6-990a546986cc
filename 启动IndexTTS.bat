@echo off
chcp 65001 >nul
title IndexTTS WebUI - 受保护版本

echo.
echo ========================================
echo 🚀 IndexTTS WebUI - 受保护版本
echo ========================================
echo.

if not exist "webui_protected.py" (
    echo ❌ 找不到程序文件: webui_protected.py
    echo 💡 请确保所有文件完整
    pause
    exit /b 1
)

echo 📂 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo 🚀 启动程序...
echo.

python webui_protected.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 💡 请检查错误信息
    pause
)
