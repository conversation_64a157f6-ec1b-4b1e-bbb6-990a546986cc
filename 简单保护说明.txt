# IndexTTS WebUI 字节码保护版本使用说明

## 文件说明
- `webui.pyc`: 编译后的字节码文件（主程序）
- `webui_launcher.py`: 启动器脚本
- `启动WebUI.bat`: 一键启动脚本
- `简单保护说明.txt`: 本说明文件
- `预设样音/`: 预设音频文件夹

## 使用方法
1. 双击 `启动WebUI.bat` 启动程序
2. 或者在命令行运行: `python webui_launcher.py`
3. 程序会自动打开浏览器访问WebUI界面

## 保护特性
- ✅ 源代码已编译为Python字节码
- ✅ 无法直接查看原始源代码
- ✅ 修改字节码文件会导致程序无法运行
- ✅ 保留完整功能，用户体验不变
- ✅ 简单有效的基础保护

## 安全说明
- 字节码保护防止代码被轻易查看
- 虽然理论上可以反编译，但增加了逆向难度
- 适合一般用户的基础保护需求
- 任何修改都会导致程序无法正常运行

## 注意事项
- 请勿修改 `webui.pyc` 文件
- 确保Python环境正常安装
- 如遇问题请查看控制台错误信息
- 建议保留原始备份文件

## 分发说明
分发时请包含以下文件：
- webui.pyc (必需)
- webui_launcher.py (必需)
- 启动WebUI.bat (推荐)
- 预设样音/ (如果需要)
- 简单保护说明.txt (推荐)

## 故障排除
如果程序无法启动：
1. 检查Python是否正确安装
2. 确保所有依赖库已安装
3. 以管理员身份运行
4. 查看详细错误信息
