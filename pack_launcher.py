#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包启动器的脚本
将launcher.py和webui.py一起打包
"""

import os
import sys
import subprocess
import shutil

def pack_launcher():
    """打包启动器"""
    print("=" * 60)
    print("🚀 IndexTTS WebUI 启动器打包工具")
    print("=" * 60)
    
    # 检查文件
    required_files = ["launcher.py", "webui.py"]
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少文件: {file}")
            return False
        print(f"✅ 文件存在: {file}")
    
    # 检查PyInstaller
    try:
        result = subprocess.run(['pyinstaller', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ PyInstaller: {result.stdout.strip()}")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ PyInstaller未安装，正在安装...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
            print("✅ PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    # 清理之前的构建
    print("🧹 清理之前的构建文件...")
    dirs_to_clean = ["dist", "build", "__pycache__"]
    files_to_clean = ["*.spec"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    import glob
    for pattern in files_to_clean:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"   删除文件: {file_path}")
    
    # 打包启动器
    print("📦 开始打包启动器...")
    
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个文件
        '--console',  # 保留控制台
        '--name', 'IndexTTS-WebUI',  # 指定输出文件名
        '--add-data', 'webui.py;.',  # 将webui.py作为数据文件包含
        '--add-data', '预设样音;预设样音',  # 添加预设样音文件夹
        '--hidden-import', 'gradio',
        '--hidden-import', 'torch',
        '--hidden-import', 'librosa',
        '--hidden-import', 'soundfile',
        '--hidden-import', 'numpy',
        '--hidden-import', 'pandas',
        '--hidden-import', 'importlib.util',
        'launcher.py'
    ]
    
    print(f"   执行命令: {' '.join(cmd)}")
    print("   这可能需要几分钟时间，请耐心等待...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 打包成功")
        else:
            print("❌ 打包失败")
            print("错误信息:")
            print(result.stderr)
            return False
    
    except subprocess.TimeoutExpired:
        print("❌ 打包超时")
        return False
    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return False
    
    # 验证输出
    exe_path = os.path.join("dist", "IndexTTS-WebUI.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path)
        size_mb = file_size / (1024 * 1024)
        print(f"✅ 可执行文件已生成: {exe_path}")
        print(f"   文件大小: {size_mb:.1f} MB")
        
        # 创建使用说明
        create_launcher_readme()
        
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print("=" * 60)
        print("📁 输出目录: dist/")
        print("📦 程序文件: dist/IndexTTS-WebUI.exe")
        print("📖 使用说明: dist/README.txt")
        print("\n💡 提示:")
        print("   - 程序已打包为单个exe文件")
        print("   - webui.py代码已内嵌，难以提取")
        print("   - 可以安全分发给用户使用")
        print("   - 建议测试运行确保功能正常")
        
        return True
    else:
        print(f"❌ 可执行文件未找到: {exe_path}")
        return False

def create_launcher_readme():
    """创建启动器的使用说明"""
    readme_content = """# IndexTTS WebUI 打包版使用说明

## 文件说明
- `IndexTTS-WebUI.exe`: 打包后的主程序（包含启动器和webui代码）
- `README.txt`: 本说明文件

## 使用方法
1. 双击 `IndexTTS-WebUI.exe` 启动程序
2. 程序会自动加载内嵌的webui模块
3. 程序会自动打开浏览器访问WebUI界面
4. 如果浏览器没有自动打开，请手动访问控制台显示的地址

## 注意事项
- 首次运行可能需要较长时间加载
- 请确保系统已安装必要的运行库
- 如遇到问题，请查看控制台输出的错误信息
- 程序会自动创建outputs文件夹用于输出

## 安全特性
- webui.py代码已内嵌到exe文件中
- 代码已编译为字节码，难以直接提取
- 通过启动器间接运行，增加保护层级
- 防止简单的代码查看和修改

## 故障排除
如果程序无法启动，请尝试：
1. 以管理员身份运行
2. 检查防病毒软件是否误报
3. 确保系统已安装Visual C++ Redistributable
4. 查看控制台错误信息

## 技术说明
本程序使用启动器模式：
- launcher.py: 启动器脚本，负责加载和运行webui模块
- webui.py: 主程序代码，作为数据文件内嵌到exe中
- 运行时动态导入webui模块，避免PyInstaller的字节码分析问题
"""
    
    os.makedirs("dist", exist_ok=True)
    with open("dist/README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✅ 使用说明已创建: dist/README.txt")

if __name__ == "__main__":
    success = pack_launcher()
    
    if success:
        print("\n✅ 打包成功完成！")
        sys.exit(0)
    else:
        print("\n❌ 打包失败！")
        sys.exit(1)
