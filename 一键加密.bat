@echo off
chcp 65001 >nul
title IndexTTS WebUI 一键加密工具

echo.
echo ========================================
echo 🚀 IndexTTS WebUI 一键加密工具
echo ========================================
echo.

echo 📋 检查环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 💡 请先安装Python并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 📦 安装必要工具...
echo 正在安装PyArmor...
pip install pyarmor -q
if errorlevel 1 (
    echo ⚠️ PyArmor安装可能失败，尝试继续...
)

echo 正在安装PyInstaller...
pip install pyinstaller -q
if errorlevel 1 (
    echo ⚠️ PyInstaller安装可能失败，尝试继续...
)

echo.
echo 🔐 开始加密打包...
python encrypt_webui.py

if errorlevel 1 (
    echo.
    echo ❌ 加密打包失败！
    echo 💡 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ✅ 加密打包完成！
echo.
echo 📁 输出文件位置: dist\IndexTTS-WebUI.exe
echo 📖 使用说明: dist\README.txt
echo.
echo 💡 提示:
echo    - 可执行文件已完全加密
echo    - 可以安全分发给用户
echo    - 建议先测试运行确保功能正常
echo.

pause
